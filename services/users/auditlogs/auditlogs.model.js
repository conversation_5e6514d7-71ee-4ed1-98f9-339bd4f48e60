const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {AUDIT_LOG, USER} = require('../../../constants/dbCollections');
const {AUDIT_LOG_ACTIONS} = require('../../../constants/constant');

const schema = new mongoose.Schema({
  userId: {type: mongoose.Schema.Types.ObjectId, ref: USER, required: true, index: true},
  action: {type: String, required: true, enum: AUDIT_LOG_ACTIONS, index: true},
  details: {type: mongoose.Schema.Types.Mixed, default: {}},
  ipAddress: {type: String, index: true},
  userAgent: {type: String},
  success: {type: Boolean, default: true, index: true},
  errorMessage: {type: String},
  metadata: {type: mongoose.Schema.Types.Mixed, default: {}},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

// Indexes for better query performance
schema.index({userId: 1, createdAt: -1});
schema.index({action: 1, createdAt: -1});
schema.index({ipAddress: 1, createdAt: -1});
schema.index({success: 1, createdAt: -1});

// TTL index to automatically delete old logs after 1 year
schema.index({createdAt: 1}, {expireAfterSeconds: 365 * 24 * 60 * 60});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(AUDIT_LOG, schema, AUDIT_LOG);
