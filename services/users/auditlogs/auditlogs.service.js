const Model = require('./auditlogs.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'auditlogs',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  
  settings: {
    fields: ['_id', 'userId', 'action', 'details', 'ipAddress', 'userAgent', 'success', 'errorMessage', 'metadata', 'createdAt'],
    populates: {
      userId: {
        action: 'users.get',
        params: {
          fields: 'email fullName _id'
        }
      }
    },
    populateOptions: ['userId']
  },

  actions: {
    log: {
      params: {
        userId: { type: 'string' },
        action: { type: 'string' },
        details: { type: 'object', optional: true },
        ipAddress: { type: 'string', optional: true },
        userAgent: { type: 'string', optional: true },
        success: { type: 'boolean', optional: true },
        errorMessage: { type: 'string', optional: true },
        metadata: { type: 'object', optional: true }
      },
      async handler(ctx) {
        const logData = {
          ...ctx.params,
          success: ctx.params.success !== false // Default to true
        };

        return await this.adapter.insert(logData);
      }
    },

    getUserLogs: {
      rest: 'GET /user/:userId',
      params: {
        userId: { type: 'string' },
        page: { type: 'number', optional: true, default: 1 },
        limit: { type: 'number', optional: true, default: 20 },
        action: { type: 'string', optional: true },
        success: { type: 'boolean', optional: true }
      },
      auth: 'required',
      async handler(ctx) {
        const { userId, page, limit, action, success } = ctx.params;
        
        // Check if user can access these logs (admin or own logs)
        if (ctx.meta.user._id !== userId && !ctx.meta.user.isSystemAdmin) {
          throw new MoleculerClientError('Access denied', 403);
        }

        const query = { userId };
        if (action) query.action = action;
        if (success !== undefined) query.success = success;

        const options = {
          page,
          limit,
          sort: { createdAt: -1 },
          populate: this.settings.populateOptions
        };

        return await this.adapter.paginate(query, options);
      }
    },

    getSecurityEvents: {
      rest: 'GET /security/:userId',
      params: {
        userId: { type: 'string' },
        days: { type: 'number', optional: true, default: 30 }
      },
      auth: 'required',
      async handler(ctx) {
        const { userId, days } = ctx.params;
        
        // Check if user can access these logs
        if (ctx.meta.user._id !== userId && !ctx.meta.user.isSystemAdmin) {
          throw new MoleculerClientError('Access denied', 403);
        }

        const securityActions = [
          'LOGIN', 'FAILED_LOGIN', 'PASSWORD_CHANGE', 'PASSWORD_RESET', 
          'ACCOUNT_LOCKED', 'TWO_FACTOR_ENABLED', 'TWO_FACTOR_DISABLED'
        ];

        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        const logs = await this.adapter.find({
          query: {
            userId,
            action: { $in: securityActions },
            createdAt: { $gte: startDate }
          },
          sort: { createdAt: -1 },
          populate: this.settings.populateOptions
        });

        return logs;
      }
    },

    getFailedLogins: {
      rest: 'GET /failed-logins',
      params: {
        hours: { type: 'number', optional: true, default: 24 },
        limit: { type: 'number', optional: true, default: 100 }
      },
      admin: true,
      async handler(ctx) {
        const { hours, limit } = ctx.params;
        
        const startDate = new Date();
        startDate.setHours(startDate.getHours() - hours);

        const logs = await this.adapter.find({
          query: {
            action: 'FAILED_LOGIN',
            createdAt: { $gte: startDate }
          },
          sort: { createdAt: -1 },
          limit,
          populate: this.settings.populateOptions
        });

        return logs;
      }
    },

    getStats: {
      rest: 'GET /stats',
      params: {
        days: { type: 'number', optional: true, default: 7 }
      },
      admin: true,
      async handler(ctx) {
        const { days } = ctx.params;
        
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        const stats = await this.adapter.collection.aggregate([
          {
            $match: {
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: '$action',
              count: { $sum: 1 },
              successCount: {
                $sum: { $cond: ['$success', 1, 0] }
              },
              failureCount: {
                $sum: { $cond: ['$success', 0, 1] }
              }
            }
          },
          {
            $sort: { count: -1 }
          }
        ]);

        return stats;
      }
    }
  },

  methods: {
    /**
     * Helper method to log user actions
     */
    async logUserAction(userId, action, details = {}, ctx = null) {
      const logData = {
        userId,
        action,
        details,
        success: true
      };

      if (ctx) {
        logData.ipAddress = ctx.meta.clientIP || ctx.meta.$remoteAddress;
        logData.userAgent = ctx.meta.userAgent;
      }

      return await this.actions.log(logData);
    },

    /**
     * Helper method to log failed actions
     */
    async logFailedAction(userId, action, errorMessage, details = {}, ctx = null) {
      const logData = {
        userId,
        action,
        details,
        success: false,
        errorMessage
      };

      if (ctx) {
        logData.ipAddress = ctx.meta.clientIP || ctx.meta.$remoteAddress;
        logData.userAgent = ctx.meta.userAgent;
      }

      return await this.actions.log(logData);
    }
  },

  events: {
    'user.login'(payload) {
      // this.logUserAction(payload.userId, 'LOGIN', {
      //   email: payload.email
      // }, payload.ctx);
    },

    'user.logout'(payload) {
      this.logUserAction(payload.userId, 'LOGOUT', {}, payload.ctx);
    },

    'user.registered'(payload) {
      this.logUserAction(payload._id, 'REGISTER', {
        email: payload.email,
        fullName: payload.fullName
      });
    },

    'user.password.changed'(payload) {
      this.logUserAction(payload.userId, 'PASSWORD_CHANGE', {}, payload.ctx);
    },

    'user.failed.login'(payload) {
      this.logFailedAction(
        payload.userId || 'unknown',
        'FAILED_LOGIN',
        payload.error,
        { email: payload.email },
        payload.ctx
      );
    }
  }
};
