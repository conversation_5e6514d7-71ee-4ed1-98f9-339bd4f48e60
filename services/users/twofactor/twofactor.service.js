const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const { MoleculerClientError } = require('moleculer').Errors;
const { generateSecureToken } = require('../../../helpers/usersHelper');
const i18next = require('i18next');

module.exports = {
  name: 'twofactor',
  
  actions: {
    generateSecret: {
      rest: 'POST /generate-secret',
      auth: 'required',
      async handler(ctx) {
        const { userID } = ctx.meta;
        
        const user = await ctx.call('users.get', { id: userID });
        if (!user) {
          throw new MoleculerClientError(i18next.t('error_user_not_found'), 404);
        }

        // Check if 2FA is already enabled
        if (user.twoFactorAuth?.enabled) {
          throw new MoleculerClientError('Two-factor authentication is already enabled', 400);
        }

        // Generate secret
        const secret = speakeasy.generateSecret({
          name: `${user.email}`,
          issuer: 'Demo App',
          length: 32
        });

        // Generate QR code
        const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

        // Store secret temporarily (not enabled yet)
        await ctx.call('users.update', {
          id: userID,
          'twoFactorAuth.secret': secret.base32,
          'twoFactorAuth.enabled': false
        });

        return {
          secret: secret.base32,
          qrCode: qrCodeUrl,
          manualEntryKey: secret.base32,
          backupCodes: this.generateBackupCodes()
        };
      }
    },

    enable: {
      rest: 'POST /enable',
      params: {
        token: { type: 'string', length: 6 }
      },
      auth: 'required',
      async handler(ctx) {
        const { userID } = ctx.meta;
        const { token } = ctx.params;
        
        const user = await ctx.call('users.get', { 
          id: userID,
          fields: 'twoFactorAuth'
        });
        
        if (!user || !user.twoFactorAuth?.secret) {
          throw new MoleculerClientError('No 2FA secret found. Generate secret first.', 400);
        }

        if (user.twoFactorAuth.enabled) {
          throw new MoleculerClientError('Two-factor authentication is already enabled', 400);
        }

        // Verify token
        const verified = speakeasy.totp.verify({
          secret: user.twoFactorAuth.secret,
          encoding: 'base32',
          token: token,
          window: 2
        });

        if (!verified) {
          throw new MoleculerClientError('Invalid verification code', 400);
        }

        // Generate backup codes
        const backupCodes = this.generateBackupCodes();

        // Enable 2FA
        await ctx.call('users.update', {
          id: userID,
          'twoFactorAuth.enabled': true,
          'twoFactorAuth.backupCodes': backupCodes,
          'twoFactorAuth.lastUsedAt': new Date()
        });

        // Emit event for audit logging
        this.broker.emit('user.twofactor.enabled', {
          userId: userID,
          ctx
        });

        return {
          success: true,
          message: 'Two-factor authentication enabled successfully',
          backupCodes: backupCodes
        };
      }
    },

    disable: {
      rest: 'POST /disable',
      params: {
        password: { type: 'string', min: 1 },
        token: { type: 'string', length: 6, optional: true },
        backupCode: { type: 'string', optional: true }
      },
      auth: 'required',
      async handler(ctx) {
        const { userID } = ctx.meta;
        const { password, token, backupCode } = ctx.params;
        
        const user = await ctx.call('users.get', { 
          id: userID,
          fields: 'password twoFactorAuth'
        });
        
        if (!user.twoFactorAuth?.enabled) {
          throw new MoleculerClientError('Two-factor authentication is not enabled', 400);
        }

        // Verify password
        const { comparePassword } = require('../../../helpers/usersHelper');
        if (!comparePassword(password, user.password)) {
          throw new MoleculerClientError('Invalid password', 400);
        }

        // Verify 2FA token or backup code
        let verified = false;
        
        if (token) {
          verified = speakeasy.totp.verify({
            secret: user.twoFactorAuth.secret,
            encoding: 'base32',
            token: token,
            window: 2
          });
        } else if (backupCode) {
          verified = user.twoFactorAuth.backupCodes?.includes(backupCode);
          if (verified) {
            // Remove used backup code
            const updatedCodes = user.twoFactorAuth.backupCodes.filter(code => code !== backupCode);
            await ctx.call('users.update', {
              id: userID,
              'twoFactorAuth.backupCodes': updatedCodes
            });
          }
        }

        if (!verified) {
          throw new MoleculerClientError('Invalid verification code or backup code', 400);
        }

        // Disable 2FA
        await ctx.call('users.update', {
          id: userID,
          'twoFactorAuth.enabled': false,
          'twoFactorAuth.secret': null,
          'twoFactorAuth.backupCodes': [],
          'twoFactorAuth.lastUsedAt': null
        });

        // Emit event for audit logging
        this.broker.emit('user.twofactor.disabled', {
          userId: userID,
          ctx
        });

        return {
          success: true,
          message: 'Two-factor authentication disabled successfully'
        };
      }
    },

    verify: {
      params: {
        userId: { type: 'string' },
        token: { type: 'string', length: 6, optional: true },
        backupCode: { type: 'string', optional: true }
      },
      async handler(ctx) {
        const { userId, token, backupCode } = ctx.params;
        
        const user = await ctx.call('users.get', { 
          id: userId,
          fields: 'twoFactorAuth'
        });
        
        if (!user.twoFactorAuth?.enabled) {
          return { verified: false, reason: '2FA not enabled' };
        }

        let verified = false;
        let usedBackupCode = false;

        if (token) {
          verified = speakeasy.totp.verify({
            secret: user.twoFactorAuth.secret,
            encoding: 'base32',
            token: token,
            window: 2
          });
        } else if (backupCode) {
          verified = user.twoFactorAuth.backupCodes?.includes(backupCode);
          usedBackupCode = verified;
        }

        if (verified) {
          // Update last used time
          await ctx.call('users.update', {
            id: userId,
            'twoFactorAuth.lastUsedAt': new Date()
          });

          // Remove used backup code
          if (usedBackupCode) {
            const updatedCodes = user.twoFactorAuth.backupCodes.filter(code => code !== backupCode);
            await ctx.call('users.update', {
              id: userId,
              'twoFactorAuth.backupCodes': updatedCodes
            });
          }
        }

        return { 
          verified,
          usedBackupCode,
          remainingBackupCodes: usedBackupCode ? 
            user.twoFactorAuth.backupCodes.length - 1 : 
            user.twoFactorAuth.backupCodes?.length || 0
        };
      }
    },

    getBackupCodes: {
      rest: 'GET /backup-codes',
      auth: 'required',
      async handler(ctx) {
        const { userID } = ctx.meta;
        
        const user = await ctx.call('users.get', { 
          id: userID,
          fields: 'twoFactorAuth'
        });
        
        if (!user.twoFactorAuth?.enabled) {
          throw new MoleculerClientError('Two-factor authentication is not enabled', 400);
        }

        return {
          backupCodes: user.twoFactorAuth.backupCodes || [],
          count: user.twoFactorAuth.backupCodes?.length || 0
        };
      }
    },

    regenerateBackupCodes: {
      rest: 'POST /regenerate-backup-codes',
      params: {
        password: { type: 'string', min: 1 }
      },
      auth: 'required',
      async handler(ctx) {
        const { userID } = ctx.meta;
        const { password } = ctx.params;
        
        const user = await ctx.call('users.get', { 
          id: userID,
          fields: 'password twoFactorAuth'
        });
        
        if (!user.twoFactorAuth?.enabled) {
          throw new MoleculerClientError('Two-factor authentication is not enabled', 400);
        }

        // Verify password
        const { comparePassword } = require('../../../helpers/usersHelper');
        if (!comparePassword(password, user.password)) {
          throw new MoleculerClientError('Invalid password', 400);
        }

        // Generate new backup codes
        const backupCodes = this.generateBackupCodes();

        await ctx.call('users.update', {
          id: userID,
          'twoFactorAuth.backupCodes': backupCodes
        });

        return {
          success: true,
          backupCodes: backupCodes,
          message: 'Backup codes regenerated successfully'
        };
      }
    },

    getStatus: {
      rest: 'GET /status',
      auth: 'required',
      async handler(ctx) {
        const { userID } = ctx.meta;
        
        const user = await ctx.call('users.get', { 
          id: userID,
          fields: 'twoFactorAuth'
        });
        
        return {
          enabled: user.twoFactorAuth?.enabled || false,
          lastUsedAt: user.twoFactorAuth?.lastUsedAt,
          backupCodesCount: user.twoFactorAuth?.backupCodes?.length || 0
        };
      }
    }
  },

  methods: {
    generateBackupCodes() {
      const codes = [];
      for (let i = 0; i < 10; i++) {
        codes.push(generateSecureToken(8).toUpperCase());
      }
      return codes;
    }
  }
};
