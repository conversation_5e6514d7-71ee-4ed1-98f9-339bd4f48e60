const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const { USER_SESSION } = require('../../../constants/dbCollections');

const schema = new mongoose.Schema({
  userId: {type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true, index: true},
  refreshToken: {type: String, required: true, index: true},
  deviceInfo: {
    userAgent: { type: String },
    browser: { type: String },
    os: { type: String },
    device: { type: String },
    ip: { type: String, index: true }
  },
  expiresAt: {type: Date, required: true, index: true}
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt'
  },
  versionKey: false
});

// Indexes for better query performance
schema.index({ userId: 1, isActive: 1 });
schema.index({ userId: 1, lastActivityAt: -1 });
schema.index({ sessionId: 1, isActive: 1 });

// TTL index to automatically delete expired sessions
schema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(USER_SESSION, schema, USER_SESSION);
