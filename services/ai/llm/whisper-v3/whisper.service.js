'use strict';

const fs = require('fs');
const {OpenAI} = require('openai');
const Groq = require('groq-sdk');

module.exports = {
  name: 'whisperV3',
  hooks: {},
  settings: {},
  dependencies: [],
  actions: {
    transcriptAudio: {
      timeout: 2 * 60 * 1000,
      rest: 'GET /transcript',
      async handler(ctx) {
        const {audioPath, model = 'whisper-large-v3', apiKey} = ctx.params;
        try {
          const sizeCheck = await this.checkSize(audioPath);
          if (sizeCheck > 20) {
            return {
              error: 'File size is greater than 20MB, try smaller video',
            };
          }
          const groq = new Groq({apiKey});
          const transcription = await groq.audio.transcriptions.create({
            model: model,
            file: fs.createReadStream(audioPath),
            prompt: 'Specify context or spelling',
          });
          return transcription;
        } catch (err) {
          return err;
        }
      },
    },
  },

  events: {},

  methods: {
    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        // Convert the file size to megabytes (optional)
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },
  },

  created() {},
  async started() {},
  async stopped() {},
};
