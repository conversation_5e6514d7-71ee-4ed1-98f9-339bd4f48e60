const fs = require('fs');
const {OpenAI} = require('openai');
const FunctionsCommon = require('../../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'chatgpt',
  mixins: [FunctionsCommon],
  settings: {},
  hooks: {},

  dependencies: [],
  actions: {
    transcriptAudio: {
      timeout: 5 * 60 * 1000,
      rest: 'GET /transcript',
      async handler(ctx) {
        const {audioPath, apiKey} = ctx.params;
        try {
          const sizeCheck = await this.checkSize(audioPath);
          if (sizeCheck > 9.96) {
            return {
              error: 'File size is greater than 10MB, try smaller video',
            };
          }
          const openai = new OpenAI({apiKey});
          return await openai.audio.transcriptions.create({
            model: 'whisper-1',
            file: fs.createReadStream(audioPath),
          });
        } catch (err) {
          return err;
        }
      },
    },
    chatCompletion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /chatCompletion',
      async handler(ctx) {
        try {
          const {messages, model, schema, responseFormat, responseId, temperature, max_tokens, apiKey} = ctx.params;
          const openai = new OpenAI({apiKey});
          let body = {
            model: model,
            messages: [...messages],
            max_tokens: max_tokens,
          };
          if (responseFormat === 'json_object') {
            body = {
              ...body,
              messages: [...messages],
              tools: [{
                type: 'function',
                function: {name: 'show_response', description: 'Show the response', parameters: schema},
              }],
              tool_choice: {type: 'function', function: {name: 'show_response'}},
              temperature,
            };
          }
          const completion = await openai.chat.completions.create(body);
          const {completion_tokens, prompt_tokens, total_tokens} = completion.usage;
          this.broker.emit('llmGenerateCompleted', {
            id: responseId,
            completionTokens: completion_tokens,
            promptTokens: prompt_tokens,
            totalTokens: total_tokens,
            gptModel: model,
          });
          if (responseFormat === 'json_object') {
            const generatedText = completion.choices[0].message.tool_calls[0].function.arguments;
            console.log('generatedText', JSON.parse(generatedText));
            return JSON.parse(generatedText);
          }
          return completion.choices[0].message.content;

        } catch (err) {
          console.log(err);
          return err;
        }
      },
    },
    completion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /completion',
      async handler(ctx) {
        const {model, max_tokens, prompt, apiKey} = ctx.params;

        let body = {
          model: model,
          prompt,
          max_tokens: parseInt(max_tokens),
        };
        const openai = new OpenAI({apiKey});
        const completion = await openai.chat.completions.create(body);
        return completion.choices[0].message.content;
      },
    },
  },

  events: {},
  methods: {
    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        // Convert the file size to megabytes (optional)
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },
  },
  created() {},
  async started() {},
  async stopped() {},
};
