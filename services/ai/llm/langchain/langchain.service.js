const fs = require("fs");
const {OpenAI} = require("openai");
const {ChatOpenAI, OpenAIEmbeddings} = require("@langchain/openai");
const {ChatPromptTemplate} = require("@langchain/core/prompts");
const {ChatGoogleGenerativeAI} = require("@langchain/google-genai");
const {CacheBackedEmbeddings} = require("langchain/embeddings/cache_backed");
const {InMemoryStore} = require("langchain/storage/in_memory");
const {similarity} = require("ml-distance");

const model = "text-embedding-3-small"
const apiKey = "***************************************************"

const underlyingEmbeddings = new OpenAIEmbeddings({apiKey, model});
const inMemoryStore = new InMemoryStore();
const cacheBackedEmbeddings = CacheBackedEmbeddings.fromBytesStore(underlyingEmbeddings, inMemoryStore, {
  namespace: underlyingEmbeddings.modelName,
});

module.exports = {
  name: "langchain",
  settings: {},
  hooks: {},

  dependencies: [],
  actions: {
    transcriptAudio: {
      timeout: 5 * 60 * 1000,
      rest: 'GET /transcript',
      async handler(ctx) {
        const audioPath = ctx.params.audioPath;
        try {
          const sizeCheck = await this.checkSize(audioPath);
          if (sizeCheck > 9.96) {
            return {
              error: "File size is greater than 10MB, try smaller video",
            };
          }
          const {apiKey} = ctx.meta;
          const openai = new OpenAI({apiKey});

          return await openai.audio.transcriptions.create({
            model: "whisper-1",
            file: fs.createReadStream(audioPath),
          });
        } catch (err) {
          return err;
        }
      },
    },

    chatCompletion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /chatCompletion',
      async handler(ctx) {
        try {
          let {
            messages,
            model = "gpt-3.5-turbo-1106" || "gpt-4-1106-preview" || "gpt-3.5-turbo-0613",
            modelInterface = 'ChatOpenAI',
            schema,
            responseFormat,
            responseId,
            temperature,
            apiKey,
          } = ctx.params;
          apiKey = apiKey || await this.getAPIKey(ctx)

          const chatModel = this.getModel(ctx, modelInterface, {
            apiKey: apiKey || ctx.meta.apiKey, model, temperature, maxTokens: 4000
          });
          const promptMessages = this.transformMessages(modelInterface, messages);

          const prompt = ChatPromptTemplate.fromMessages(promptMessages);
          const completion = {};
          // const steamResponse = await chain.stream()
          if (responseFormat === 'json_object') {
            // const chain = prompt.pipe(chatModel.bindTools([formatContentTool]))
            const chain = prompt.pipe(chatModel.withStructuredOutput(schema));
            const result = await chain.invoke('');
            completion.usage = {
              completion_tokens: 0, prompt_tokens: 0, total_tokens: 0,
            };
            completion.content = result;
          } else {
            const chain = prompt.pipe(chatModel);
            const result = await chain.invoke('');
            completion.usage = {
              completion_tokens: result.input_tokens,
              prompt_tokens: result.output_tokens,
              total_tokens: result.total_tokens,
            };
            completion.content = result.content;
          }

          // const completion = await openai.chat.completions.create(body);
          const {completion_tokens, prompt_tokens, total_tokens} = completion.usage;
          this.broker.emit("llmGenerateCompleted", {
            id: responseId,
            completionTokens: completion_tokens,
            promptTokens: prompt_tokens,
            totalTokens: total_tokens,
            gptModel: model
          })

          return completion.content;

        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },

    getEmbedding: {
      timeout: 5 * 60 * 1000, async handler(ctx) {
        try {
          let {
            textContent,
          } = ctx.params;

          let time = Date.now();
          const result = await cacheBackedEmbeddings.embedDocuments([textContent]);

          // console.log(`Creation time: ${Date.now() - time}ms`);
          // console.log("textContent", textContent, result[0].length, result[0][0] );
          return result[0];
        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },
    getRelatedScore: {
      timeout: 5 * 60 * 1000,
      async handler(ctx) {
        try {
          let {
            vectors,
          } = ctx.params;

          // console.log("vectors", vectors);
          const score = similarity.cosine(vectors[0], vectors[1])
          // console.log("similarity", vectors[0].length, vectors[1].length, score);
          // console.log("similarity", vectors[0][0], vectors[1][0], score);

          return score
        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },
  },

  events: {},
  methods: {
    getModel(ctx, modelInterface, modelOptions) {
      if (modelInterface === "ChatOpenAI") {
        return new ChatOpenAI(modelOptions);
      }
      if (modelInterface === "GoogleGenerativeAI") {
        return new ChatGoogleGenerativeAI(modelOptions);
      }
      return new ChatOpenAI(modelOptions);
    },
    transformMessages(modelInterface, messages) {
      const promptMessages = messages.map(mes => [mes.role, mes.content]);
      if (modelInterface === "ChatOpenAI") {
        return promptMessages;
      }
      if (modelInterface === "GoogleGenerativeAI") {
        return this.transformToGemini(promptMessages);
      }
      return promptMessages;
    },
    transformToGemini(messagesChatgpt) {
      let messagesGemini = [];
      let systemPrompt = [];

      messagesChatgpt.forEach(message => {
        const [role, content] = message;
        if (role === 'system') {
          systemPrompt.push(message[1]);
        } else if (role === 'user') {
          messagesGemini.push([role, content]);
        } else if (role === 'assistant') {
          messagesGemini.push([role, content]);
        }
      });

      if (systemPrompt) {
        messagesGemini[0][1] = `*${systemPrompt.join('\n')}*\n` + messagesGemini[0][1];
      }

      return messagesGemini;
    },

    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        // Convert the file size to megabytes (optional)
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },

    async getAPIKey(ctx) {
      const setting = await ctx.call("settings.findOne");
      return ctx.meta.apiKey = setting?.apiKeyOpenAI || process.env.OPENAI_API_KEY;
    },
  },


  created() {},
  async started() {},
  async stopped() {},
};
