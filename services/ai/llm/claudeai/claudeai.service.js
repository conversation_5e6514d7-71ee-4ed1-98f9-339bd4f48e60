const {Anthropic} = require("@anthropic-ai/sdk");
const FunctionsCommon = require('../../../../mixins/functionsCommon.mixin');

/** @type {ServiceSchema} */
module.exports = {
  name: "claudea<PERSON>",

  mixins: [FunctionsCommon],
  settings: {}, hooks: {}, /**
   * Dependencies
   */
  dependencies: [],

  /**
   * Actions
   */
  actions: {
    /**
     * Say a 'Hello' action.
     *
     * @returns
     */

    chatCompletion: {
      timeout: 5 * 60 * 1000, rest: {
        method: "POST",
        path: "/chatCompletion",
      }, // visibility: "protected",
      async handler(ctx) {
        try {
          let {
            messages,
            model,
            schema,
            responseFormat,
            responseId,
            temperature,
            max_tokens,
            apiKey
          } = ctx.params;
          apiKey = apiKey || await this.getAPIKey(ctx)
          const anthropic = new Anthropic({apiKey});
          const {systemPrompt, completeMessages} = await this.transformMessages(messages)
          let body = {
            model: model || "claude-3-5-sonnet-20241022",
            messages: [...completeMessages],
            max_tokens: 8192,
            system: systemPrompt,
            temperature
          };
          if (responseFormat === 'json_object') {
            body = {
              ...body,
              tools: [
                {
                  name: "show_response", description: "Show the response",
                  input_schema: schema
                }
              ],
            };
          }
          const response = await anthropic.messages.create(body);
          console.log("completeMessages", response.content[1].input)

          return responseFormat === 'json_object' ? response.content[1].input : response.content[0].text;
        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },
  },

  /**
   * Events
   */
  events: {},

  /**
   * Methods
   */
  methods: {
    async transformMessages(messages) {
      const systemPrompt = messages[0]?.content || '';

      const completeMessages = await Promise.all(messages
        .filter(({ role }) => role === 'user')
        .map(async ({ content, ...rest }) => ({
          ...rest,
          content: Array.isArray(content) ? await Promise.all(content.map(async ({ type, image_url, ...contentRest }) => {
            if (type === "image_url" || type !== "text") {
              const imageUrl = image_url.url;
              const { mediaType, imageData } = await this.transformImageUrl(imageUrl);
              return {
                type: "image",
                source: {
                  type: "base64",
                  media_type: mediaType,
                  data: imageData,
                }
              };
            }
            return { type, ...contentRest };
          })) : content
        }))
      );

      return {
        systemPrompt,
        completeMessages
      };
    },

    isBase64Image(string) {
      // Check if the string matches base64 image format
      const base64Pattern = /^data:image\/(png|jpeg|jpg|gif|bmp|webp);base64,[A-Za-z0-9+/=]+$/;
      return base64Pattern.test(string);
    },

    async transformImageUrl(imageUrl) {
      if (this.isBase64Image(imageUrl)) {
        const imageData = imageUrl.split(',')[1];
        return {
          mediaType: this.imageTypeFromBase64(imageData),
          imageData
        }
      }

      const imageArrayBuffer = await ((await fetch(imageUrl)).arrayBuffer());
      const imageData = Buffer.from(imageArrayBuffer).toString('base64');
      return {
        mediaType: this.getMediaTypeFromUrl(imageUrl),
        imageData
      }
    },
    getMediaTypeFromUrl(url) {
      const extensionToMediaType = {
        png: "image/png",
        jpg: "image/jpeg",
        jpeg: "image/jpeg",
        gif: "image/gif",
        bmp: "image/bmp",
        webp: "image/webp",
        svg: "image/svg+xml",
      };

      try {
        const urlObj = new URL(url); // Validate the URL
        const extension = urlObj.pathname.split('.').pop().toLowerCase();
        return extensionToMediaType[extension] || "unknown";
      } catch (error) {
        return "invalid url";
      }
    },
    async getAPIKey(ctx) {
      const setting = await ctx.call("settings.findOne");
      return ctx.meta.apiKey = setting?.apiKeyOpenAI || process.env.OPENAI_API_KEY;
    },
  },

  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
