const fs = require('fs');
const {GoogleGenAI, Type} = require('@google/genai');
const FunctionsCommon = require('../../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'gemini',
  mixins: [FunctionsCommon],
  settings: {},
  hooks: {},

  dependencies: [],
  actions: {
    transcriptAudio: {
      timeout: 5 * 60 * 1000,
      rest: 'GET /transcript',
      async handler(ctx) {
        const {audioPath, apiKey} = ctx.params;
        try {
          const sizeCheck = await this.checkSize(audioPath);
          if (sizeCheck > 9.96) {
            return {
              error: 'File size is greater than 10MB, try smaller video',
            };
          }
          const openai = new OpenAI({apiKey});
          return await openai.audio.transcriptions.create({
            model: 'whisper-1',
            file: fs.createReadStream(audioPath),
          });
        } catch (err) {
          return err;
        }
      },
    },
    chatCompletion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /chatCompletion',
      async handler(ctx) {
        try {
          const {messages, model, schema, responseFormat, responseId, temperature, max_tokens, apiKey} = ctx.params;
          const gemini = new GoogleGenAI({apiKey});
          let body = {
            model: model,
            contents: [...messages],
            max_tokens: max_tokens,
          };
          if (responseFormat === 'json_object') {
            body = {
              ...body,
              contents: [...messages],
              config:{
                // thinkingConfig: {
                //   thinkingBudget: 0,
                // },
                responseMimeType: 'application/json',
                responseSchema: this.convertJsonSchema(schema),
                maxOutputTokens: max_tokens
              },
              temperature,
            };
          }

          const completion = await gemini.models.generateContent(body);
          const { promptTokenCount, candidatesTokenCount, totalTokenCount} = completion.usageMetadata;
          this.broker.emit('llmGenerateCompleted', {
            id: responseId,
            completionTokens: candidatesTokenCount,
            promptTokens: promptTokenCount,
            totalTokens: totalTokenCount,
            model: model,
          });

          if (responseFormat === 'json_object') {
            const generatedText = completion.candidates[0].content.parts[0].text;
            return JSON.parse(generatedText);
          }
          return completion.candidates[0].content.parts[0].text;
        } catch (err) {
          console.log(err);
          return err;
        }
      },
    },
    completion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /completion',
      async handler(ctx) {
        const {model, max_tokens, prompt, apiKey} = ctx.params;

        let body = {
          model: model,
          contents: prompt,
          config: {
            responseMimeType: 'text/plain',
            maxOutputTokens: max_tokens
          }
        };
        const gemini = new GoogleGenAI({apiKey});
        const completion = await gemini.models.generateContent(body);
        return completion.candidates[0].content.parts[0].text;
      },
    },
  },
  events: {},
  methods: {
    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },
    convertJsonSchema: async (schema) => {
      const jsonSchema = JSON.parse(generatedText);

    },
    converMessage: (messages) => {

    }
  },
  created() {},
  async started() {},
  async stopped() {},
};
