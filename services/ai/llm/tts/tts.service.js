const fs = require('fs');
const {OpenAI} = require('openai');

module.exports = {
  name: 'tts',
  settings: {},
  hooks: {},
  dependencies: [],

  actions: {
    textToSpeech: {
      timeout: 6 * 60 * 1000,
      rest: 'GET /textToSpeech',
      async handler(ctx) {
        const {text, voice, model = 'tts-1', speed = 1, apiKey} = ctx.params;
        try {
          const openai = new OpenAI({apiKey});
          return await openai.audio.speech.create({
            model: model,
            voice: voice || 'alloy',
            speed,
            input: text,
          });
        } catch (err) {
          return err;
        }
      },
    },
  },

  events: {},
  methods: {},
  created() {},
  async started() {},
  async stopped() {},
};
