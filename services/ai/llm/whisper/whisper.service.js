const fs = require('fs');
const {OpenAI} = require('openai');

module.exports = {
  name: 'whisper',
  hooks: {
    before: {
      '*': 'getAPIKey',
    },
  },
  settings: {},
  dependencies: [],

  actions: {
    transcriptAudio: {
      timeout: 2 * 60 * 1000,
      rest: 'GET /transcript',
      async handler(ctx) {
        const {audioPath, model = 'whisper-1', apiKey} = ctx.params;
        try {
          const sizeCheck = await this.checkSize(audioPath);
          if (sizeCheck > 20) {
            return {
              error: 'File size is greater than 20MB, try smaller video',
            };
          }
          const openai = new OpenAI({apiKey});
          return await openai.audio.transcriptions.create({
            model: model,
            file: fs.createReadStream(audioPath),
            prompt: 'Hello, welcome to my lecture.',
          });
        } catch (err) {
          return err;
        }
      },
    },

    segmentTranscript: {
      timeout: 2 * 60 * 1000,
      rest: 'GET /segmentTranscript',
      async handler(ctx) {
        const {audioPath, model = 'whisper-1', apiKey} = ctx.params;
        try {
          const sizeCheck = await this.checkSize(audioPath);
          if (sizeCheck > 20) {
            return {
              error: 'File size is greater than 20MB, try smaller video',
            };
          }
          const openai = new OpenAI({apiKey});
          return await openai.audio.transcriptions.create({
            model: model,
            file: fs.createReadStream(audioPath),
            prompt: 'Hello, welcome to my lecture.',
            response_format: 'verbose_json',
            timestamp_granularities: ['segment'],
          });
        } catch (err) {
          return err;
        }
      },
    },
  },

  events: {},
  methods: {
    checkSize: async (filePath) => {
      try {
        let stats = fs.statSync(filePath);
        let fileSizeInBytes = stats.size;
        let fileSizeInMegabytes = fileSizeInBytes / (1024 * 1024);
        return fileSizeInMegabytes;
      } catch (error) {
        return error;
      }
    },
  },

  created() {},
  async started() {},
  async stopped() {},
};
