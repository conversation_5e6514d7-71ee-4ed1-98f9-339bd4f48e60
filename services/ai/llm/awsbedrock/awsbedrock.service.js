const {AnthropicBedrock} = require("@anthropic-ai/bedrock-sdk");
const FunctionsCommon = require("../../../../mixins/functionsCommon.mixin");
const {getConfig} = require("../../../../config/config");
const config = getConfig(process.env.NODE_ENV);
module.exports = {
  name: "awsbedrock",
  mixins: [FunctionsCommon],
  settings: {},
  hooks: {},
  dependencies: [],
  actions: {
    chatCompletion: {
      timeout: 5 * 60 * 1000,
      rest: 'POST /chatCompletion',
      async handler(ctx) {
        try {
          let {messages, model, schema, responseFormat, responseId, temperature, max_tokens, apiKey} = ctx.params;
          const {awsAccessKey, awsSecretKey, awsRegion} = config
          const client = new AnthropicBedrock({awsAccessKey, awsSecretKey, awsRegion});

          const {systemPrompt, completeMessages} = await this.transformMessages(messages)
          let body = {
            model,
            anthropic_version: "bedrock-2023-05-31",
            messages: [...completeMessages],
            max_tokens: 8192,
            system: systemPrompt,
            temperature
          };
          if (responseFormat === 'json_object') {
            body = {
              ...body,
              tools: [
                {
                  name: "show_response",
                  description: "Show the response",
                  input_schema: schema
                }
              ],
            };
          }
          const response = await client.messages.create(body);
          return responseFormat === 'json_object' ? response.content[1].input : response.content[0].text;
        } catch (err) {
          console.log(err);
          return err;
        }
      }
    },
  },
  events: {},
  methods: {
    async transformMessages(messages) {
      const systemPrompt = messages[0]?.content || '';

      const completeMessages = await Promise.all(messages
        .filter(({role}) => role === 'user')
        .map(async ({content, ...rest}) => ({
          ...rest,
          content: Array.isArray(content) ? await Promise.all(content.map(async ({type, image_url, ...contentRest}) => {
            if (type === "image_url" || type !== "text") {
              const imageUrl = image_url.url;
              const {mediaType, imageData} = await this.transformImageUrl(imageUrl);
              return {
                type: "image",
                source: {
                  type: "base64",
                  media_type: mediaType,
                  data: imageData,
                }
              };
            }
            return {type, ...contentRest};
          })) : content
        }))
      );

      return {
        systemPrompt,
        completeMessages
      };
    },

    isBase64Image(string) {
      const base64Pattern = /^data:image\/(png|jpeg|jpg|gif|bmp|webp);base64,[A-Za-z0-9+/=]+$/;
      return base64Pattern.test(string);
    },

    async transformImageUrl(imageUrl) {
      if (this.isBase64Image(imageUrl)) {
        const imageData = imageUrl.split(',')[1];
        return {
          mediaType: this.imageTypeFromBase64(imageData),
          imageData
        }
      }

      const imageArrayBuffer = await ((await fetch(imageUrl)).arrayBuffer());
      const imageData = Buffer.from(imageArrayBuffer).toString('base64');
      return {
        mediaType: this.getMediaTypeFromUrl(imageUrl),
        imageData
      }
    },
    getMediaTypeFromUrl(url) {
      const extensionToMediaType = {
        png: "image/png",
        jpg: "image/jpeg",
        jpeg: "image/jpeg",
        gif: "image/gif",
        bmp: "image/bmp",
        webp: "image/webp",
        svg: "image/svg+xml",
      };

      try {
        const urlObj = new URL(url);
        const extension = urlObj.pathname.split('.').pop().toLowerCase();
        return extensionToMediaType[extension] || "unknown";
      } catch (error) {
        return "invalid url";
      }
    },
  },
  created() {},
  async started() {},
  async stopped() {},
};
