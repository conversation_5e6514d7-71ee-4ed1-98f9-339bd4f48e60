const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {MODEL_PRICE, API_KEY} = require('../../../constants/dbCollections');

const {Schema} = mongoose;
const schema = new Schema({
  tokenUnit: {type: Number},
  unit: {type: String},
  gptModel: {type: String},
  maxTokens: {type: Number},
  priceInput: {type: Number},
  priceOutput: {type: Number},
  modelInterface: {type: String},
  apiKeyIds: [{type: Schema.Types.ObjectId, ref: API_KEY}],
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(MODEL_PRICE, schema, MODEL_PRICE);
