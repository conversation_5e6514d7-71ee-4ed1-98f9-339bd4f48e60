const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {PROJECT, IMAGE, USER} = require('../../constants/dbCollections');

const schema = new Schema(
  {
    projectName: {type: Schema.Types.Mixed, required: true, validate: /\S+/},
    imageId: {type: Schema.Types.ObjectId, ref: IMAGE},
    ownerId: {type: Schema.Types.ObjectId, required: true, ref: USER},
    description: {type: Schema.Types.Mixed},
    isShowPlainText: {type: Boolean, default: false},
    isDeleted: {type: Boolean, default: false},
    isDraft: {type: Boolean, default: false},
    type: {
      type: String,
      enum: ['NORMAL', 'EXAM_SCHOOL', 'EXAM_IELTS', 'MARK_TEST_SCHOOL', 'MARK_TEST_IELTS'],
      default: 'NORMAL',
    },
    examCode: String,
    commonOptions: {type: Schema.Types.Mixed},
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
    versionKey: false,
  },
);

module.exports = mongoose.model(PROJECT, schema, PROJECT);
