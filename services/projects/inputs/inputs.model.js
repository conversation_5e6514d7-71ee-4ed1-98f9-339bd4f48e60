const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {CONTENT, USER, INPUT, TOOL} = require('../../../constants/dbCollections');

const schema = new Schema(
  {
    inputType: {type: String, required: true, validate: /\S+/},
    contentId: {type: Schema.Types.ObjectId, ref: CONTENT},
    toolId: {type: Schema.Types.ObjectId, ref: TOOL},
    userId: {type: Schema.Types.ObjectId, ref: USER},
    inputData: {type: Schema.Types.Mixed},
    plaintext: {type: String},

    isHasResponse: {type: Boolean, default: false},
    isEditing: {type: Boolean, default: false},
    numberSubmit: {type: Number, default: 1},
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
    versionKey: false,
  },
);
schema.index({contentId: 1});
module.exports = mongoose.model(INPUT, schema, INPUT);
