const DbMongoose = require('../../../mixins/dbMongo.mixin');
const InputModel = require('./inputs.model');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const i18next = require('i18next');
const {MoleculerClientError} = require('moleculer').Errors;

module.exports = {
  name: 'inputs',
  mixins: [DbMongoose(InputModel), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },

  hooks: {
    before: {
      'remove': 'checkPermission',
      '*': 'checkToolPermission',
    },
    after: {
      find(ctx, res) {
        return res;
      },
    },
  },

  actions: {
    list: {
      visibility: 'public',
    },
    create: {
      rest: {
        method: 'POST',
        path: '/',
      },
      toolPermission: true,
      async handler(ctx) {
        const entity = ctx.params;
        entity.userId = ctx.meta.user?._id;
        return await this.adapter.insert(entity);
      },
    },
    last: {
      rest: {
        method: 'GET',
        path: '/last',
      },
      auth: 'required',
      async handler(ctx) {
        const {contentId} = ctx.params;
        const [result] = await this._list(ctx, {
          query: {contentId, isDeleted: false},
          sort: '-createdAt',
          limit: 1,
        }).then(res => res.rows);
        return result;
      },
    },

    getMediaFileName: {
      rest: {
        method: 'GET',
        path: '/:id/mediaName',
      },
      auth: 'required',
      async handler(ctx) {
        try {
          const {id} = ctx.params;
          const input = await this.adapter.findById(id);
          console.log('input.inputType', input.inputType);
          switch (input.inputType) {
            case 'video':
              if (input.inputData.videoType === 'offline') {
                const offlineVideoInfo = await ctx.call('offlinevideos.get', {id: input.inputData.offlineVideoId});
                return offlineVideoInfo.name?.split('.').slice(0, -1);
              }
              const videoInfo = await ctx.call('videos.videoDetail', {url: input.inputData.url});
              return videoInfo.title;

            case 'audio':
              const fileInfo = await ctx.call('files.get', {id: input.inputData.audioId});
              return fileInfo.displayName?.split('.').slice(0, -1);
            case 'offline_video':
              const offlineVideoInfo = await ctx.call('offlinevideos.get', {id: input.inputData.offlineVideoId});
              return offlineVideoInfo.name?.split('.').slice(0, -1);
            default:
              return null;
          }
        } catch (e) {
          throw new MoleculerClientError(i18next.t('error_input_not_found'), 404);
        }
      },
    },

    statisticInput: {
      rest: {
        method: 'GET',
        path: '/statisticInput',
      },
      async handler(ctx) {
        try {
          return this.countSubmitByUser(ctx.params.query);
        } catch (e) {
          console.log(e);
        }
      },
    },
    addWorkspaceId: {
      rest: {
        method: 'GET',
        path: '/addWorkspaceId',
      },
      async handler(ctx) {
        try {
          const inputs = await this.adapter.find({
            query: {
              isDeleted: false,
              userId: {$exists: true},
            },
          });
          const contents = await ctx.call('contents.find', {
            query: {_id: {$in: inputs.map(input => input.contentId)}},
            populdate: [],
          });

          const projects = await ctx.call('projects.find', {
            query: {_id: {$in: contents.map(content => content.projectId)}},
            populdate: [],
          });

          // console.time(1);
          // for (const input of inputs) {
          //   const content = contents.find(content => content._id.toString() === input.contentId.toString());
          //   const project = projects.find(project => project._id.toString() === content.projectId.toString());
          //   input.workspaceId = project.workspaceId;
          //   await this.adapter.updateById(input._id, input);
          // }
          // console.timeEnd(1);

          console.time(2);
          const mapProject = {}, mapContent = {};
          projects.forEach(project => {
            mapProject[project._id] = project;
          });

          contents.forEach(content => {
            mapContent[content._id] = mapProject[content.projectId]?.workspaceId;
          });
          await InputModel.bulkWrite(inputs.map(row => ({
            updateOne: {
              filter: {_id: row._id},
              update: {
                $set: {
                  ...row,
                  workspaceId: mapContent[row.contentId],
                },
              },
              upsert: true,
            },
          })));
          console.timeEnd(2);

          return inputs;
        } catch (e) {
          console.log(e);
        }
      },
    },

    statisticInputTracking: {
      rest: {
        method: 'GET',
        path: '/statisticInputTracking',
      },
      async handler(ctx) {
        try {
          return this.countTrackingByUser(ctx.params.query);
        } catch (e) {
          console.log(e);
        }
      },
    },

    updateInputMarkTest: {
      rest: {
        method: 'PUT',
        path: '/:id/updateInputMarkTest',
      },
      toolPermission: true,
      async handler(ctx) {
        const entity = ctx.params;
        entity.isEditing = true;
        return await this.adapter.updateById(entity.id, entity);
      },
    },

    remove: {
      rest: {
        path: '/:id',
        method: 'DELETE',
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const input = await this.adapter.findById(id);
        await this.adapter.updateById(id, {isDeleted: true});
        await ctx.emit('inputDeleted', {inputId: id});
        return input;
      },
    },
  },
  methods: {
    countSubmitByUser(query) {
      return InputModel.aggregate([
        {$match: query},
        {
          $group: {
            _id: '$userId',
            numberSubmit: {
              $sum: 1,
            },
          },
        },
      ]);
    },
    async countTrackingByUser(query) {
      const costData = await this.broker.call('responses.statisticCostResponse', {query: {createdAt: query.createdAt}});

      return InputModel.aggregate([
        {$match: query},
        {
          $addFields: {
            cost: {$first: {$filter: {input: costData, as: 'cost', cond: {$eq: ['$$cost._id', '$_id']}}}},
          },
        },
        {
          $group: {
            _id: {userId: '$userId', toolId: '$toolId'},
            toolUsageCount: {$sum: '$numberSubmit'},
            toolCost: {$sum: '$cost.totalCost'},
          },
        },
        {$sort: {toolUsageCount: -1}},
        {
          $lookup: {
            from: 'Tool',
            localField: '_id.toolId',
            foreignField: '_id',
            as: 'tools',
          },
        },
        {$unwind: '$tools'},
        {
          $group: {
            _id: '$_id.userId',
            numberSubmit: {$sum: '$toolUsageCount'},
            toolUsed: {
              $push: {
                toolId: '$_id.toolId',
                toolName: '$tools.name',
                toolUsageCount: '$toolUsageCount',
                toolCost: '$toolCost',
                tool: '$tools',
              },
            },
            totalCost: {$sum: '$toolCost'},
          },
        },
        {
          $project: {
            toolUsed: 1,
            numberSubmit: 1,
            totalCost: 1,
          },
        },
      ]);
    },
    async checkPermission(ctx) {
      const {id} = ctx.params;
      const {user} = ctx.meta;
      if (user.isSystemAdmin) return;

      const input = await this.adapter.findById(id);
      if (!input) {
        throw new MoleculerClientError(i18next.t('error_input_not_found'), 404);
      }

      const checkPermission = await ctx.call('contents.permissionAccess', {id: input?.contentId});
      if (!checkPermission) {
        throw new MoleculerClientError(i18next.t('error_permission_denied'), 403);
      }
    },
    async checkToolPermission(ctx) {
      const {action, params, meta} = ctx;
      let {id, toolId} = params;
      if (meta.user?.isSystemAdmin) return;

      if (action?.toolPermission) {
        if (id) {
          const input = await this.adapter.findById(id);
          toolId = input.toolId;
        }

        await ctx.call('tools.checkToolPermission', {id: toolId});
      }
    },
  },
  events: {
    async 'contents.deteted'(payload, sender, event) {
      this.logger.info('payload', payload, sender, event);
      await this.adapter.updateMany({contentId: payload._id}, {isDeleted: true});
    },
    async 'contents.deleteMany'(payload, sender, event) {
      this.logger.info('payload', payload, sender, event);
      await this.adapter.updateMany({contentId: {$in: payload}}, {isDeleted: true});
    },
    async 'inputsDeleteMany'(payload, sender, event) {
      this.logger.info('payload', payload, sender, event);
      await this.adapter.updateMany({_id: payload.inputId}, {isDeleted: true});
    },
  },
};
