const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {SAMPLE_CONTENT, TOOL} = require('../../../constants/dbCollections');

const schema = new Schema(
  {
    toolId: {type: Schema.Types.ObjectId, ref: TOOL},
    input: {type: Schema.Types.Mixed},
    response: {type: Schema.Types.Mixed},
    isDeleted: {type: Boolean, default: false},
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
    versionKey: false,
  },
);
schema.index({contentId: 1});
module.exports = mongoose.model(SAMPLE_CONTENT, schema, SAMPLE_CONTENT);
