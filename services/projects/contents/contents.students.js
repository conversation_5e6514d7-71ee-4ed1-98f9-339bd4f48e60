'use strict';
const {MoleculerClientError} = require('moleculer').Errors;
const i18next = require('i18next');
const {DEFAULT_PROJECT_NAME, OUTPUT_TYPE, INPUT_TYPE} = require('../../../constants/constant');
module.exports = {
  actions: {
    submitSpeaking: {
      rest: {
        method: 'POST',
        path: '/submitSpeaking',
      },
      toolPermission: true,
      async handler(ctx) {
        const {workspaceId, contentId, inputData} = ctx.params;
        const [content, responses] = await Promise.all([
          this.adapter.findById(contentId),
          ctx.call('responses.find', {query: {contentId, isActivate: true}}),
        ]);
        const savedInput = responses[0]?.inputId;
        const savedResponse = await ctx.call('responses.update', {id: responses[0]?._id, state: 'processing'});

        const {user} = ctx.meta;
        const [permissionAccess, project] = await Promise.all([
          ctx.call('projects.permissionAccess', {id: content.projectId}),
          ctx.call('projects.get', {id: content.projectId.toString()}),
        ]);
        if (!this.editAccess(permissionAccess)) {
          throw new MoleculerClientError(i18next.t('dont_have_permission_project'), 403, 'FORBIDDEN');
        }

        if (project.projectName === DEFAULT_PROJECT_NAME.UNTITLED_SPEECH) {
          ctx.call('projects.update', {id: content.projectId, projectName: savedInput.inputData.topic});
        }

        const inputEntity = {
          ...savedInput,
          workspaceId: workspaceId,
          userId: user?._id,
          toolId: content.toolId,
          inputData: {...savedInput.inputData, ...inputData},
        };


        ctx.call('inputs.update', {id: savedInput._id, ...inputEntity});
        ctx.emit('lastModifiedProject', {id: content.projectId});
        ctx.call('contents.submitInput', {
          input: inputEntity, processingResponse: savedResponse, projectId: content.projectId,
        });
        ctx.emit('recentProject', {projectId: content.projectId, userId: ctx.meta.user?._id});
        ctx.emit('resourceUpdate', {
          inputData: inputEntity.inputData,
          inputType: savedInput.inputType,
          projectId: content.projectId,
        });
        await ctx.emit('studentSubmited', {inputType: savedInput.inputType, workspaceId});
        return {
          ...savedResponse,
          state: 'processing',
          output: {
            text: 'Hold on! We are processing',
          },
          outputType: OUTPUT_TYPE.PRON_FEEDBACK,
        };
      },
    },

    submitIeltsWriting: {
      rest: {
        method: 'POST',
        path: '/submitIeltsWriting',
      },
      toolPermission: true,
      async handler(ctx) {
        const {workspaceId, contentId, inputData, inputType, projectName} = ctx.params;
        const content = await this.adapter.findById(contentId);
        const {user} = ctx.meta;
        const permissionAccess = await ctx.call('projects.permissionAccess', {id: content.projectId});
        if (!this.editAccess(permissionAccess)) {
          throw new MoleculerClientError(i18next.t('dont_have_permission_project'), 403, 'FORBIDDEN');
        }

        const inputEntity = {
          workspaceId: workspaceId,
          inputData,
          inputType,
          userId: user?._id,
          toolId: content.toolId,
          contentId: content._id,
        };

        const savedInput = await ctx.call('inputs.insert', {entity: inputEntity});
        const processingResponse = {
          inputId: savedInput._id,
          contentId: content._id,
          toolId: content.toolId,
          output: {
            text: 'Hold on! We are processing',
          },
          state: 'processing',
          isActivate: true,
        };
        const savedResponse = await ctx.call('responses.insert', {entity: processingResponse});
        ctx.call('responses.deactivate', {
          contentId: savedResponse.contentId,
          id: savedResponse._id,
        });
        const projectEntity = projectName ? {id: content.projectId, projectName, isDraft: false} : {
          id: content.projectId,
          isDraft: false,
        };
        await ctx.call('projects.update', projectEntity);
        ctx.emit('lastModifiedProject', {id: content.projectId});

        await ctx.call('contents.submitInput', {
          input: savedInput,
          processingResponse: savedResponse,
          projectId: content.projectId,
        });

        ctx.emit('recentProject', {projectId: content.projectId, userId: ctx.meta.user?._id});
        ctx.emit('resourceUpdate', {inputData, inputType, projectId: content.projectId});
        ctx.emit('imageUpdateSubmit', {inputData});
        await ctx.emit('studentSubmited', {inputType, workspaceId});
        return ctx.call('responses.get', {id: savedResponse._id, populate: ['inputId']});
      },
    },

    generateTopic: {
      rest: {
        method: 'POST',
        path: '/generateTopic',
      },
      async handler(ctx) {
        const {category = '', tag = '', inputType} = ctx.params;

        let prompt = `Generate a comprehensive IELTS Writing Task 2 topic`;
        if (inputType === INPUT_TYPE.STUDENT_SPEAKING) {
          prompt = `Generate a IELTS Speaking Part 2 topic`;
        }

        if (tag) {
          prompt += ` about ${tag}`;
        }

        if (category) {
          prompt += ` in ${category}`;
        }
        console.log('prompt', prompt);
        const messages = [
          {role: 'system', content: prompt},
          {role: 'user', content: 'No captions, just answers'},
        ];

        const schema = {
          type: 'object',
          properties: {
            topic: {
              type: 'string',
              description: 'The generated topic',
            },
          },
          required: ['topic'],
        };

        try {
          const {apiKeyIds} = await ctx.call('gptmodelprice.findOne', {gptModel: 'gpt-4o-mini'});
          const apiKey = apiKeyIds[1]?.apiKey;
          console.log('apiKey', apiKey);
          const result = await this.broker.call('azureopenai.chatCompletion', {
            messages,
            model: 'gpt-4o-mini',
            temperature: 1,
            max_tokens: 20,
            apiKey,
            responseFormat: 'json_object',
            schema,
          });
          return result.topic;
        } catch (error) {
          console.error('Error in handler:', error.message);
        }
      },
    },
  },
  methods: {
    async checkStudentSubmitPermission(ctx) {
      const {input} = ctx.params;
      const inputType = ctx.params.inputType || input.inputType;
      const userId = ctx.meta.user?._id;

      // const permission = await ctx.call("permissions.getOne", {userId, organizationId});
      const permissions = await ctx.call('permissions.find', {query: {userId}});
      const activePermissions = permissions.filter(item => item.subscriptionId.status === 'ACTIVE');
      const isSpeaking = inputType === INPUT_TYPE.STUDENT_SPEAKING;
      const usedKey = isSpeaking ? 'speakingUsed' : 'writingUsed';
      const limitKey = isSpeaking ? 'speakingLimit' : 'writingLimit';

      const haveAccess = activePermissions.find(item => {
        return Number(item.accessLimit[limitKey]) > Number(item.accessLimit[usedKey]) && item.subscriptionId.startDate <= new Date();
      });
      const msg = isSpeaking ? 'submit_speaking_limited' : 'submit_writing_limited';
      if (!haveAccess) throw new MoleculerClientError(i18next.t(msg), 400, 'LIMIT');
    },
  },
  events: {},
};
