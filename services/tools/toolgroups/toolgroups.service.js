const DbMongoose = require('../../../mixins/dbMongo.mixin');
const Model = require('./toolgroups.model');
const BaseService = require('../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'toolgroups',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    entityValidator: {},
  },

  hooks: {
    before: {},
  },

  actions: {},
  methods: {
    async seedDB() {
      const toolsDefine = require('./toolgroups.seed.json');

      await this.adapter.bulkWrite(toolsDefine.map(row => ({
        updateOne: {
          filter: {'localization.groupName': row.localization.groupName},
          update: {$set: {...row, isDeleted: false}},
          upsert: true,
        },
      })), {ordered: false});
    },
  },
  events: {},
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};
