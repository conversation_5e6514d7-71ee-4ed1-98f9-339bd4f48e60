const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {CONVERSATION, INSTRUCTION} = require('../../../../constants/dbCollections');

const {Schema} = mongoose;
const schema = new Schema({
    messages: {type: Schema.Types.Mixed},
    instructionId: {type: Schema.Types.ObjectId, ref: INSTRUCTION},
    isDeleted: {type: Boolean, default: false},
  }, {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
    versionKey: false,
  },
);

schema.plugin(mongoosePaginate);
module.exports = mongoose.model(CONVERSATION, schema, CONVERSATION);
