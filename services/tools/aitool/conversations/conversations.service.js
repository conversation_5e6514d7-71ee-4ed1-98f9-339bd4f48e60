const DbMongoose = require('../../../../mixins/dbMongo.mixin');
const Model = require('./conversations.model');
const BaseService = require('../../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../../mixins/functionsCommon.mixin');
const {createMessages} = require('../promptengine');
const AdmZip = require('adm-zip');
const fs = require('fs');
const mammoth = require('mammoth');
const path = require('path');
const storageDir = path.join(__dirname, 'storage');
const FileMixin = require('../../../../mixins/file.mixin');
const {OUTPUT_TYPE} = require('../../../../constants/constant');

module.exports = {
  name: 'conversations',
  mixins: [DbMongoose(Model), FileMixin, BaseService, FunctionsCommon],
  settings: {
    entityValidator: {},
    populates: {
      'intructionId': 'intructions.get',
    },
    populateOptions: ['instructionId'],
  },

  actions: {
    copy: {
      rest: 'POST /copy',
      async handler(ctx) {
        const {conversationId} = ctx.params;
        const conversation = await this.adapter.findOne({_id: conversationId});
        return await this.adapter.insert({
          messages: conversation.messages,
          intructionId: conversation.intructionId,
        });
      },
    },
  },
  methods: {
    appendOutput(messages, output, outputType) {
      if (!output) return;

      const content = (() => {
        switch (outputType) {
          case OUTPUT_TYPE.HTML:
            return output.markdown || output.html || output.text;
          case OUTPUT_TYPE.HTML_QUESTIONS:
            return output.questionsHtml || output.text;
          default:
            return output.text;
        }
      })();

      messages.push({
        role: 'assistant',
        content,
      });
    },
    async save(stream, filename, folder) {
      const dirPath = this.getDirPath(folder, storageDir);
      const filePath = this.getFilePath(filename, dirPath);
      return this.saveToLocalStorage(stream, filePath);
    },
  },
  events: {
    async 'datasets.deleted'(payload) {
      this.adapter.removeMany({datasetId: payload});
    },
    createConversation: {
      params: {
        response: 'object',
        rating: 'string',
      },
      async handler(ctx) {
        const {response, rating} = ctx.params;
        const {output, inputId} = response;
        const {inputData} = inputId;

        const [instructionData, defaultDataset] = await Promise.all([
          ctx.call('instructions.details', {
            id: inputData.instructionId,
            populateOpts: ['optionIds.knowledgeIds', 'outputTypeId'],
          }),
          ctx.call('datasets.findOne', {
            query: {
              isDefault: true,
              isDeleted: false,
              instructionId: inputData.instructionId,
            },
          }),
        ]);

        const datasetId = defaultDataset?._id || (await ctx.call('datasets.create', {
          instructionId: inputData.instructionId,
          name: `Default Dataset for ${instructionData.shortName}`,
          isDefault: true,
        }))._id;
        const messages = await createMessages({inputData, text: inputData.text, instructionData});
        this.appendOutput(messages, output, instructionData.outputTypeId.code || instructionData.outputType);

        await this.adapter.insert({
          messages,
          rating,
          datasetId,
          creatorId: ctx.meta.user?._id,
        });
      },
    },
  },
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};
