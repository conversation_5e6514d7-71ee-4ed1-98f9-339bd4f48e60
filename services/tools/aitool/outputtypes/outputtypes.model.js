const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {OUTPUT_TYPE} = require('../../../../constants/dbCollections');

const schema = new Schema({
  responseFormat: {type: String, enum: ['json_object', 'markdown']},
  schemaInstruction: {type: Schema.Types.Mixed},
  localization: {
    name: {
      en: {type: String, validate: /\S+/},
      vi: {type: String, validate: /\S+/},
    },
  },
  isDeleted: {type: Boolean, default: false},
}, {
  timestamps: {
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  versionKey: false,
});

module.exports = mongoose.model(OUTPUT_TYPE, schema, OUTPUT_TYPE);
