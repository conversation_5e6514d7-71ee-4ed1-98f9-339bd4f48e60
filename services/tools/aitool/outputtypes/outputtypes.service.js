const DbMongoose = require('../../../../mixins/dbMongo.mixin');
const Model = require('./outputtypes.model');
const BaseService = require('../../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../../mixins/functionsCommon.mixin');

module.exports = {
  name: 'outputtypes',
  mixins: [DbMongoose(Model), BaseService, FunctionsCommon],
  settings: {
    /** Validator schema for entity */
    entityValidator: {},
    populates: {},
    populateOptions: [],
  },

  hooks: {
    before: {},
  },

  actions: {},
  methods: {},
  events: {},

};
