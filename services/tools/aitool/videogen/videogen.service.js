const DbMongoose = require('../../../../mixins/dbMongo.mixin');
const BaseService = require('../../../../mixins/baseService.mixin');
const FunctionsCommon = require('../../../../mixins/functionsCommon.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const axios = require('axios');
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');

module.exports = {
  name: 'videogen',
  mixins: [BaseService, FunctionsCommon],
  settings: {
    entityValidator: {},
  },

  actions: {
    generateVideo: {
      rest: 'POST /generate',
      params: {
        description: 'string',
        duration: {type: 'number', optional: true, default: 30},
        style: {type: 'string', optional: true, default: 'realistic'},
        resolution: {type: 'string', optional: true, default: '1080p'},
      },
      async handler(ctx) {
        try {
          const {description, duration, style, resolution} = ctx.params;
          
          this.logger.info('Starting video generation:', {description, duration, style, resolution});

          // Step 1: Generate script from description
          const script = await this.generateScript(ctx, description, duration);
          
          // Step 2: Generate images for each scene
          const scenes = await this.generateScenes(ctx, script);
          
          // Step 3: Generate voiceover
          const voiceover = await this.generateVoiceover(ctx, script.narration);
          
          // Step 4: Combine into video
          const videoUrl = await this.combineVideo(scenes, voiceover, duration);
          
          return {
            success: true,
            videoUrl,
            script,
            scenes: scenes.length,
            duration,
            style,
            resolution
          };
          
        } catch (error) {
          this.logger.error('Video generation failed:', error);
          throw new MoleculerClientError('Video generation failed: ' + error.message, 500);
        }
      },
    },

    getProgress: {
      rest: 'GET /progress/:jobId',
      params: {
        jobId: 'string',
      },
      async handler(ctx) {
        const {jobId} = ctx.params;
        // Implementation for tracking video generation progress
        return {
          jobId,
          status: 'processing', // processing, completed, failed
          progress: 75, // percentage
          estimatedTime: 120, // seconds remaining
        };
      },
    },
  },

  methods: {
    async generateScript(ctx, description, duration) {
      try {
        // Use AI to generate a structured script
        const prompt = `Create a ${duration}-second video script for: "${description}"
        
        Return a JSON object with:
        - scenes: array of scene descriptions (3-5 scenes)
        - narration: text for voiceover
        - timing: timing for each scene
        
        Example format:
        {
          "scenes": [
            {"description": "Opening scene showing...", "duration": 5},
            {"description": "Main content with...", "duration": 20},
            {"description": "Closing scene with...", "duration": 5}
          ],
          "narration": "Welcome to our video about...",
          "totalDuration": 30
        }`;

        const aiResult = await ctx.call('chatgpt.chatCompletion', {
          messages: [
            {role: 'system', content: 'You are a professional video script writer.'},
            {role: 'user', content: prompt}
          ],
          responseFormat: 'json_object',
          max_tokens: 1000,
        });

        return JSON.parse(aiResult);
      } catch (error) {
        this.logger.error('Script generation failed:', error);
        throw error;
      }
    },

    async generateScenes(ctx, script) {
      try {
        const scenes = [];
        
        for (const scene of script.scenes) {
          // Generate image for each scene using AI image generator
          const imagePrompt = `${scene.description}, cinematic style, high quality, professional lighting`;
          
          const imageResult = await ctx.call('images.generateImage', {
            prompt: imagePrompt,
            style: 'cinematic',
            resolution: '1920x1080',
          });
          
          scenes.push({
            ...scene,
            imageUrl: imageResult.imageUrl,
            imageId: imageResult.imageId,
          });
        }
        
        return scenes;
      } catch (error) {
        this.logger.error('Scene generation failed:', error);
        throw error;
      }
    },

    async generateVoiceover(ctx, narration) {
      try {
        // Generate voiceover using TTS
        const audioResult = await ctx.call('audios.textToSpeech', {
          text: narration,
          voice: 'professional',
          speed: 1.0,
          format: 'mp3',
        });
        
        return audioResult;
      } catch (error) {
        this.logger.error('Voiceover generation failed:', error);
        throw error;
      }
    },

    async combineVideo(scenes, voiceover, duration) {
      try {
        const outputPath = path.join(__dirname, '../../../../public/videos', `video_${Date.now()}.mp4`);
        
        // Ensure output directory exists
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
          fs.mkdirSync(outputDir, {recursive: true});
        }

        return new Promise((resolve, reject) => {
          let command = ffmpeg();
          
          // Add images as input
          scenes.forEach((scene, index) => {
            command = command.input(scene.imageUrl);
          });
          
          // Add voiceover
          if (voiceover && voiceover.audioUrl) {
            command = command.input(voiceover.audioUrl);
          }
          
          // Configure video settings
          command
            .complexFilter([
              // Create video from images with transitions
              ...scenes.map((scene, index) => 
                `[${index}:v]scale=1920:1080:force_original_aspect_ratio=decrease,pad=1920:1080:(ow-iw)/2:(oh-ih)/2,setpts=PTS-STARTPTS,fps=30[v${index}]`
              ),
              // Concatenate all video segments
              scenes.map((_, index) => `[v${index}]`).join('') + `concat=n=${scenes.length}:v=1:a=0[outv]`
            ])
            .outputOptions([
              '-map', '[outv]',
              '-map', `${scenes.length}:a`, // Map audio from voiceover
              '-c:v', 'libx264',
              '-c:a', 'aac',
              '-shortest',
              '-t', duration.toString()
            ])
            .output(outputPath)
            .on('end', () => {
              const videoUrl = `/videos/${path.basename(outputPath)}`;
              resolve(videoUrl);
            })
            .on('error', (err) => {
              this.logger.error('FFmpeg error:', err);
              reject(err);
            })
            .run();
        });
        
      } catch (error) {
        this.logger.error('Video combination failed:', error);
        throw error;
      }
    },

    async cleanupTempFiles(files) {
      for (const file of files) {
        try {
          if (fs.existsSync(file)) {
            fs.unlinkSync(file);
          }
        } catch (error) {
          this.logger.warn('Failed to cleanup temp file:', file, error);
        }
      }
    },
  },

  events: {},

  async started() {
    this.logger.info('Video Generator service started');
  },

  async stopped() {
    this.logger.info('Video Generator service stopped');
  },
};
