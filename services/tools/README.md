# Tools Service Documentation

## Tổng quan
Service Tools quản lý các công cụ AI và non-AI trong hệ thống. Đây là service chính xử lý việc tạo, quản lý và thực thi các tools.

## Cấu trúc thư mục

```
services/tools/
├── aitool/                     # AI Tools components
│   ├── instructions/           # Quản lý instructions cho AI
│   ├── options/               # Quản lý options cho tools
│   ├── outputtypes/           # Quản lý output types
│   └── promptengine/          # Engine xử lý prompts
├── favoritetools/             # Quản lý tools yêu thích
├── nonaitool/                 # Non-AI tools
│   ├── convert/               # Tools chuyển đổi
│   ├── excels/               # Tools Excel
│   ├── qrcode/               # Tools QR Code
│   └── shortlink/            # Tools rút gọn link
├── toolgroups/               # Quản lý nhóm tools
├── tools.ai.js              # AI logic chính
├── tools.model.js           # MongoDB model
├── tools.service.js         # Service chính
└── tools.seed.json          # Dữ liệu seed
```

## API Endpoints

### Tools Management

#### GET /api/tools/allTools
Lấy tất cả tools (admin only)

#### GET /api/tools/availableTools
Lấy tools có sẵn cho user (yêu cầu authentication)

#### POST /api/tools/copy
Copy một tool (admin only)
```json
{
  "toolId": "string"
}
```

#### DELETE /api/tools/:id
Xóa tool (soft delete, admin only)

#### GET /api/tools/:toolId/findOne
Tìm tool theo toolId (admin only)

### AI Processing

#### POST /api/tools/submit
Submit input để xử lý bằng AI
```json
{
  "input": {
    "inputData": {},
    "inputType": "text|html|video|audio|image|file"
  },
  "response": {}
}
```

## Input Types

- `text`: Văn bản thuần
- `html`: HTML content
- `html_trim_nbsp`: HTML với loại bỏ &nbsp;
- `video`: Video từ YouTube
- `offline_video`: Video offline
- `audio`: File audio
- `image`: Hình ảnh
- `file`: File documents
- `topic`: Chủ đề
- `mark_test`: Chấm bài test
- `tts`: Text to speech

## Output Types

- `text`: Văn bản thuần
- `html`: HTML content
- `markdown`: Markdown format
- `json_object`: JSON structured data
- `audio`: Audio file
- `html_questions`: HTML questions format
- `mark_test_writing`: Writing test results

## Environment Variables

Xem file `.env.example` để biết các biến môi trường cần thiết.

## Lỗi đã sửa

1. ✅ Import `toolsModel` không đúng trong `seedDB()`
2. ✅ Syntax error trong REST endpoint
3. ✅ Action name mismatch (`instruction.get` → `instructions.get`)
4. ✅ File name typo (`ouputtypes.model.js` → `outputtypes.model.js`)
5. ✅ Logic bug trong copy function
6. ✅ Hardcoded credentials → Environment variables
7. ✅ Thêm error handling và validation

## Cải thiện đề xuất

### 1. Testing
- Thêm unit tests cho các methods
- Integration tests cho API endpoints
- Mock external services

### 2. Performance
- Implement caching cho frequently accessed tools
- Optimize database queries
- Add pagination cho large datasets

### 3. Security
- Input sanitization
- Rate limiting
- API key validation

### 4. Monitoring
- Add metrics collection
- Health check endpoints
- Error tracking

### 5. Documentation
- API documentation với Swagger
- Code comments
- Usage examples
