const FunctionsCommon = require('../../mixins/functionsCommon.mixin');
const {completionOptionCreator} = require('./aitool/promptengine');
const {getConfig} = require('../../config/config');
const i18next = require('i18next');
const {INPUT_TYPE, OUTPUT_TYPE} = require('../../constants/constant');
const config = getConfig(process.env.NODE_ENV);
const {MoleculerClientError} = require('moleculer').Errors;

module.exports = {
  mixins: [FunctionsCommon],
  settings: {
    entityValidator: {},
    populates: {
      instructionIds: {
        action: 'instructions.get',
        params: {
          fields: 'shortName outputType _id showAdditionalRequest outputTypeId optionIds',
        },
      },
      groupToolIds: 'toolgroups.get',
    },
    populateOptions: ['instructionIds.outputTypeId', 'instructionIds.optionIds', 'groupToolIds'],
  },

  dependencies: [],

  actions: {
    submit: {
      rest: 'POST /submit',
      async handler(ctx) {
        const {input: {inputData, inputType}, response} = ctx.params;

        const instructionData = await this.broker.call('instructions.details', {
          id: inputData.instructionId, populateOpts: ['outputTypeId'],
        });

        const inputText = await this.processInput(inputData, inputType, response._id);
        const emptyInput = this.handleResponseWithoutInputText(inputText, inputType);
        if (emptyInput) return emptyInput;

        if (!instructionData) {
          return await this.handleResponseWithoutInstruction(ctx, inputType, inputText, inputData);
        }

        const {
          maxTokens,
          modelInterface,
          configKey,
        } = await this.broker.call('modalais.getOneByGptModel', {gptModel: instructionData.gptModel});

        // Creating completion options based on instructionData
        const completionOptions = await completionOptionCreator(instructionData)(inputText, inputData, inputType);
        // Generating completionParams for further processing
        const completionParams = this.makeCompletionParams(response, inputData, instructionData, completionOptions);

        const responseFormat = instructionData.outputTypeId?.responseFormat || instructionData.responseFormat;
        // Calling chatCompletion service to get an AI result
        const serviceName = this.getServiceName(modelInterface);

        const aiResult = await ctx.call(`${serviceName}.chatCompletion`, {
          ...completionParams,
          responseFormat,
          responseId: response._id,
          temperature: instructionData.temperature,
          configKey,
          modelInterface,
          max_tokens: maxTokens,
        });

        // // Initializing the result object
        const outputType = instructionData.outputTypeId?.code || instructionData.outputType;
        const result = {
          success: aiResult !== undefined && aiResult !== null,
          outputType,
          output: this.convertOutput(responseFormat, aiResult, outputType, inputText, inputData),
        };

        if (instructionData?.outputTypeId?.responseFormat === 'json_object' && outputType !== OUTPUT_TYPE.PRON_FEEDBACK) {
          const schema = instructionData.outputTypeId?.schemaInstruction || instructionData.schemaInstruction;
          const isDataValid = this.checkValidSchema(schema, result.output);
          if (!isDataValid) return new Error();
        }
        const convertJsonToPlaintext = (json) => JSON.stringify(json, null, 2);

        if (result.output.json && !result.output.text) {
          result.output.text = convertJsonToPlaintext(result.output.json);
        }

        result.plaintext = this.getPlaintext(result.outputType, result.output);

        result.responseHeadline = instructionData.responseHeadline || instructionData.shortName;

        result.lastMessages = this.lastMessagesCreator(inputData, response, aiResult, completionOptions);
        return result || new Error();
      },
    },
  },
  events: {},
  methods: {
    convertOutput(responseFormat, aiResult, outputType, inputText, inputData) {
      try {
        let output = {};
        const convertMarkdown = (markdown) => {
          const html = this.convertMarkDownToHTML(markdown);
          const text = this.convertHTMLToText(html) || markdown;
          return {html, text};
        };
        switch (responseFormat) {
          case 'text':
            output.text = aiResult;
            break;
          case 'markdown':
            const {html, text} = convertMarkdown(aiResult);
            output.markdown = aiResult;
            output.html = html;
            output.text = text;
            break;
          case 'json_object':
            output = aiResult;
            if (outputType === OUTPUT_TYPE.HTML_QUESTIONS) {
              const questions = convertMarkdown(output.questions);
              output.questionsHtml = questions.html;
              output.questionsText = questions.text;

              if (output.correctAnswers) {
                const answers = convertMarkdown(output.correctAnswers);
                output.answersHtml = answers.html;
                output.answersText = answers.text;
              }
            }

            if (outputType === OUTPUT_TYPE.MARK_TEST_WRITING) {
              const evaluation = convertMarkdown(output?.evaluation);
              const answers = convertMarkdown(inputText);
              output.evaluation = inputText ? `<h2>Answers</h2><p>${answers.html}</p><br>${evaluation.html}` : evaluation.html;
            }
            if (outputType === OUTPUT_TYPE.MARK_TEST_IELTS_WRITING) {
              const evaluation = convertMarkdown(output?.evaluation);
              const answers = convertMarkdown(inputText);
              output.evaluation = inputText ? `<h2>Answers</h2><p>${answers.html}</p><br>${evaluation.html}` : evaluation.html;
            }
            if (outputType === OUTPUT_TYPE.PRON_FEEDBACK) {
              const markdown = output.evaluations.map(section =>
                `## ${section.sectionName}\n\n${section.feedback}\n\n`,
              ).join('');

              const {html} = convertMarkdown(markdown);
              output.html = html.replace(/<table[^>]*>[\s\S]*?<\/table>/g, table => {
                return table
                  .replace(/<th>/g, '<th style="border: 1px solid black;"><b>')
                  .replace(/<\/th>/g, '<\/b><\/th>')
                  .replace(/<td>/g, '<td style="border: 1px solid black;">');
              });

              output.evaluations.forEach(section => {
                section.feedback = this.convertMarkDownToHTML(section.feedback);
              });
            }
            break;
        }

        if (outputType === OUTPUT_TYPE.PRON_FEEDBACK) {
          output = {...inputData, ...output};
        }

        return output;

      } catch (e) {
        console.log(e);
        return aiResult;
      }
    },
    handleResponseWithoutInputText(inputText, inputType) {
      const typesRequiringNoInputText = new Set([
        INPUT_TYPE.MARK_TEST,
        INPUT_TYPE.MARK_TEST_IMAGE,
        INPUT_TYPE.MARK_TEST_TASK_1,
        INPUT_TYPE.MARK_TEST_TASK_2,
        INPUT_TYPE.STUDENT_TASK_1,
        INPUT_TYPE.STUDENT_TASK_2,
      ]);

      if (!inputText) {
        if (!typesRequiringNoInputText.has(inputType) && (inputType !== INPUT_TYPE.NONE)) {
          return this.responseObject(inputText, inputType);
        }

        return {
          success: true,
          outputType: OUTPUT_TYPE.MARK_TEST_WRITING,
          output: {
            evaluation: i18next.t('cannot_evaluate_without_content'),
            score: 0,
          },
        };
      }
    },
    async handleResponseWithoutInstruction(ctx, inputType, inputText, inputData) {
      if (inputType === INPUT_TYPE.TTS) {
        const {voice, speed} = inputData;
        const audio = await ctx.call('audios.textToSpeech', {text: inputText, voice, speed});
        return {
          success: true,
          outputType: OUTPUT_TYPE.AUDIO,
          output: {
            audio,
          },
          completionTokens: 0,
          gptModel: 'tts-1',
          promptTokens: inputText.length,
          totalTokens: inputText.length,
        };
      }
      return this.responseObject(inputText);
    },
    htmlWithoutImage(html) {
      return html.replace(/<figure[^>]*>.*?<\/figure>/g, '<figure></figure>');
    },
    makeCompletionParams(response, inputData, instructionData, completionOptions) {
      const {additionalRequest, isExamProject} = inputData;
      if (!response.lastMessages || !additionalRequest || isExamProject) return completionOptions;
      return {
        ...completionOptions,
        messages: [...response.lastMessages, {role: 'user', content: additionalRequest}],
      };
    },
    lastMessagesCreator(inputData, response, aiResult, completionOptions) {
      const {additionalRequest, isExamProject} = inputData;
      if (!!response.lastMessages && additionalRequest && !isExamProject) {
        return [...response.lastMessages, {role: 'user', content: additionalRequest}, {
          role: 'assistant',
          content: JSON.stringify(aiResult),
        }];
      }
      return [...completionOptions.messages, {role: 'assistant', content: JSON.stringify(aiResult)}];
    },
    async processInput(inputData, inputType, responseId) {
      try {
        switch (inputType) {
          case INPUT_TYPE.TEXT:
          case INPUT_TYPE.HTML:
            return inputData?.text;
          case INPUT_TYPE.HTML_TRIM_NBSP:
            return inputData?.text?.replace(/&nbsp;/g, '');
          case INPUT_TYPE.VIDEO:
            if (inputData.videoType === 'offline') {
              return await this.processOfflineVideoInputTranscript(inputData, inputType);
            }
            return await this.processVideoInputTranscript(inputData, inputType);
          case INPUT_TYPE.OFFLINE_VIDEO:
            return await this.processOfflineVideoInputTranscript(inputData, inputType);
          case INPUT_TYPE.TOPIC:
            return inputData.topic;
          case INPUT_TYPE.AUDIO:
            return await this.processAudioInputTranscript(inputData, inputType);
          case INPUT_TYPE.IMAGE:
            return await this.processImageInputTranscript(inputData, inputType, responseId);
          case INPUT_TYPE.FILE:
            return await this.processFileInputTranscript(inputData, inputType);
          default:
            return inputData.text;
        }
      } catch (e) {
        return null;
      }
    },
    async processVideoInputTranscript(inputData, inputType) {
      if (inputData.videoType !== 'youtube') {
        throw new Error('Video type invalid, video type is not youtube');
      }
      const {url, cutStart, cutEnd} = inputData;
      return this.broker.call('videos.videoTranscript', {
        url, cutStart, cutEnd,
      });

    },
    async processOfflineVideoInputTranscript(inputData, inputType) {
      if (inputData.videoType !== 'offline') {
        throw new Error('Video type invalid, video type is not offline');
      }
      const {offlineVideoId, cutStart, cutEnd} = inputData;

      return this.broker.call('offlinevideos.offlineVideoTranscript', {
        offlineVideoId, cutStart, cutEnd,
      });
    },
    async processAudioInputTranscript(inputData, inputType) {
      if (inputType !== INPUT_TYPE.AUDIO) {
        throw new Error('inputType invalid, inputType is not audio');
      }
      const {audioId, cutStart, cutEnd} = inputData;
      return this.broker.call('audios.audioTranscript', {
        audioId, cutStart, cutEnd,
      });
    },
    async processFileInputTranscript(inputData, inputType) {
      if (inputType !== INPUT_TYPE.FILE) {
        throw new Error('inputType invalid, inputType is not file');
      }
      const {fileId, startPage: firstPage, endPage: lastPage, totalPages} = inputData;
      const response = await this.broker.call('files.extractTextFromFileId', {
        id: fileId,
        firstPage,
        lastPage,
        totalPages,
      });
      return response.text;
    },
    async processImageInputTranscript(inputData, inputType, responseId) {
      if (inputType !== INPUT_TYPE.IMAGE) {
        throw new Error('inputType invalid, inputType is not audio');
      }
      const {imageId, topicImageId} = inputData;
      return this.broker.call('images.describeImage', {
        imageId: topicImageId || imageId, responseId,
      });
    },
    getPlaintext(outputType, output) {
      const outputMap = {
        html_questions: this.htmlQuestionsOutput,
        mark_test_writing: this.markTestWritingOutput,
        mark_test_ielts_writing: this.markTestWritingOutput,
        options: this.optionsOutput,
        open_question: this.questionOutput,
        tf_question: this.trueFalseQuestionOutput,
        multi_choice: this.questionOutput,

        fill_gaps: this.fillGapsOutput,
        dialogues: this.dialoguesOutput,
        matching_words: this.matchingOutput,
        words: this.wordsOutput,
        scramble_words: this.scrambleQuestions,
        writing_task: this.writingTaskOutput,
        advantages_and_disadvantages: this.advantagesDisadvantagesOutput,
        essay_topics: this.essayTopicsOutput,
      };

      return (outputMap[outputType] || ((output) => output.text) || ((output) => output))(output);
    },
    writingTaskOutput(output) {
      return `Writing tasks:\n${this.onlyOptionsOutput(output)}`;
    },
    essayTopicsOutput(output) {
      const {personalEssayTopics, generalEssayTopics} = output;

      const {firstListFormatted, secondListFormatted} = this.twoListOutput(personalEssayTopics, generalEssayTopics);

      return `Personal essay topics:\n${firstListFormatted}\nGeneral essay topics:\n${secondListFormatted}`;
    },
    dialoguesOutput(output) {
      const {dialogues} = output;
      return dialogues?.map(dialogue => `A. ${dialogue.personA}\nB. ${dialogue.personB}`).join('\n');
    },
    matchingOutput(output) {
      const {inputWords, translations, correctMatches} = output;

      const english = inputWords ? inputWords
        .sort((a, b) => a.wordId - b.wordId)
        .map((pair, index) => `\t${index + 1}. ${pair.word}`)
        .join('\n') : '';

      const translated = translations ? translations
        .map((pair) => `\t${pair.translationId}. ${pair.translation}`)
        .join('\n') : '';

      const matches = correctMatches ? correctMatches.map(match => `${match.wordId}. ${match.translationId}`).join('\t') : '';
      return `Words:\n${english}\n\nTranslation:\n${translated}\n\nCorrect matches:\n${matches}`;
    },
    trueFalseQuestionOutput(output) {
      const {questions, correctAnswers} = output;
      const formattedQuestions = this.formatQuestions(questions);
      const trueFalseAnswers = this.generateTrueFalseAnswers(correctAnswers);
      return `${this.lineBreak(formattedQuestions)}${trueFalseAnswers}`;
    },
    questionOutput(output) {
      const {questions, options, correctAnswers} = output;
      const formattedQuestions = questions ? this.formatQuestions(questions) : this.onlyOptionsOutput(output);
      const formattedAnswers = correctAnswers ? this.formatAnswers(correctAnswers) : '';
      return `${formattedQuestions}\n${formattedAnswers}`;
    },
    optionsOutput(output) {
      const {correctOptionId} = output;
      let questions = this.onlyOptionsOutput(output);
      return `${questions}\n${correctOptionId ? `Correct answers: ${correctOptionId}` : ''}`;
    },
    htmlQuestionsOutput(output) {
      const {questionsText, answersText} = output;
      return `${questionsText}\n${answersText ? `Correct answers: ${answersText}` : ''}`;
    },
    markTestWritingOutput(output) {
      const {evaluation} = output;
      return this.convertHTMLToText(evaluation);
    },
    advantagesDisadvantagesOutput(output) {
      const {advantages, disadvantages} = output;

      const {firstListFormatted, secondListFormatted} = this.twoListOutput(advantages, disadvantages);

      return `Advantages:\n${firstListFormatted}\nDisadvantages:\n${secondListFormatted}`;
    },
    fillGapsOutput(output) {
      const {text, correctAnswers} = output;
      const formattedAnswers = this.formatAnswers(correctAnswers);
      return `${text}\n\n${formattedAnswers}`;
    },
    twoListOutput(firstList, secondList) {
      const firstListFormatted = firstList ? firstList.map(first => `${first.itemId}. ${first.text}`).join('\n') : '';
      const secondListFormatted = secondList ? secondList.map(second => `${second.itemId}. ${second.text}`).join('\n') : '';
      return {firstListFormatted, secondListFormatted};
    },
    onlyOptionsOutput(output) {
      const {options} = output;

      return options ? options.map(({optionId, text, author}, index) => {
        return `${index + 1}. ${text}\n${author ? `-- ${author}\n` : ''}`;
      }).join('') : '';
    },
    wordsOutput(output) {
      const {words} = output;
      return words?.map(word => `${word}\n`).join('');
    },
    generateTrueFalseAnswers(correctAnswers) {
      let answers = 'Correct answers:\n';
      correctAnswers?.forEach(({correctAnswer, questionId, answerExplain}, index) => {
        answers += `${index + 1}. ${correctAnswer}${correctAnswer.toLowerCase() === 'false' ? ` -- ${answerExplain}` : ''}\n`;
      });
      return answers;
    },
    formatQuestions(questionsList) {
      let stringQuestions = 'Questions:';
      questionsList?.forEach(({question, questionId, options}) => {
        stringQuestions += `\n${questionId}. ${question}`;
        options?.forEach(({optionId, text}) => {
          stringQuestions += `\n${optionId}. ${text}`;
        });
      });
      return stringQuestions;
    },
    scrambleQuestions(output) {
      const {questions, correctAnswers} = output;
      const formattedQuestions = questions?.map(({question, questionId}, index) => {
        const questionText = question.join(' / ');
        return `${index + 1}. ${questionText}`;
      }).join('\n') + '\n';
      const formattedAnswers = this.formatAnswers(correctAnswers);
      return formattedQuestions + formattedAnswers;

    },
    formatAnswers(answersList) {
      const answers = answersList?.map(({correctAnswer, questionId}, index) => {
        return `${index + 1}. ${correctAnswer}`;
      }).join('\n');
      return answers ? `Correct answers:\n${answers}` : '';
    },
    getNoInputText(inputType) {
      const messages = {
        text: i18next.t('no_input_text'),
        topic: i18next.t('no_input_text'),
        image: i18next.t('image_no_text'),
        audio: i18next.t('audio_no_text'),
        video: i18next.t('video_no_text'),
        offline_video: i18next.t('video_no_text'),
        file: i18next.t('file_no_text'),
        mark_test: {
          evaluation: i18next.t('cannot_evaluate_without_content'),
          score: 0,
        },
        default: i18next.t('no_input_text'),
      };

      return messages[inputType] || messages.default;
    },
    responseObject(inputText, inputType) {
      const text = inputType ? this.getNoInputText(inputType) : inputText;
      return {
        success: true, outputType: OUTPUT_TYPE.TEXT, output: {text}, plaintext: text,
      };
    },
    getServiceName(modelInterface = null) {
      const serviceMap = {
        'ClaudeAI': 'claudeai',
        'AzureOpenAI': 'azureopenai',
        'AWSBedrock': 'awsbedrock',
        'GoogleGenerativeAI': 'gemini',
        'ChatOpenAI': 'chatgpt',
      };

      return serviceMap[modelInterface] || 'chatgpt';
    },
  },
  created() {
  },
  async started() {
  },
  async stopped() {
  },
  async afterConnected() {
  },
};
