const { ServiceBroker } = require('moleculer');
const ToolsService = require('../tools.service');

describe('Tools Service', () => {
  let broker;
  let service;

  beforeAll(async () => {
    broker = new ServiceBroker({ logger: false });
    service = broker.createService(ToolsService);
    await broker.start();
  });

  afterAll(async () => {
    await broker.stop();
  });

  describe('Actions', () => {
    describe('copy', () => {
      it('should throw error if toolId is missing', async () => {
        await expect(
          broker.call('tools.copy', {})
        ).rejects.toThrow('Tool ID is required');
      });

      it('should throw error if tool not found', async () => {
        // Mock adapter.findById to return null
        service.adapter.findById = jest.fn().mockResolvedValue(null);

        await expect(
          broker.call('tools.copy', { toolId: 'nonexistent' })
        ).rejects.toThrow();
      });
    });

    describe('remove', () => {
      it('should soft delete tool', async () => {
        const mockTool = { _id: 'test-id', name: 'Test Tool' };
        service.adapter.findOne = jest.fn().mockResolvedValue(mockTool);
        service.adapter.updateById = jest.fn().mockResolvedValue(mockTool);

        const result = await broker.call('tools.remove', { id: 'test-id' });
        
        expect(service.adapter.updateById).toHaveBeenCalledWith(
          'test-id',
          { isDeleted: true },
          { new: true }
        );
        expect(result).toEqual(mockTool);
      });
    });

    describe('getDetailTools', () => {
      it('should throw error if user not authenticated', async () => {
        await expect(
          broker.call('tools.getDetailTools', {}, { meta: {} })
        ).rejects.toThrow('User not authenticated');
      });
    });
  });

  describe('Methods', () => {
    describe('seedDB', () => {
      it('should handle empty seed data gracefully', async () => {
        // Mock require to return empty array
        jest.doMock('../tools.seed.json', () => []);
        
        const loggerWarnSpy = jest.spyOn(service.logger, 'warn');
        
        await service.seedDB();
        
        expect(loggerWarnSpy).toHaveBeenCalledWith('Tools seed data is empty or invalid');
      });

      it('should handle seed errors gracefully', async () => {
        service.adapter.bulkWrite = jest.fn().mockRejectedValue(new Error('DB Error'));
        const loggerErrorSpy = jest.spyOn(service.logger, 'error');
        
        await expect(service.seedDB()).rejects.toThrow('DB Error');
        expect(loggerErrorSpy).toHaveBeenCalled();
      });
    });
  });
});
