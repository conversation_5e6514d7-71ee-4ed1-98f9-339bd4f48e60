'use strict';
const FileMixin = require('../../../../mixins/file.mixin');
const fs = require('fs');
const path = require('path');
const {spawn} = require('child_process');
const https = require('https');
const {STORAGE} = require('../../../../constants/constant');
const {MoleculerClientError} = require('moleculer').Errors;
const storageDir = path.join(__dirname, 'storage');
const ytdl = require('@distube/ytdl-core');
const playDl = require('play-dl');

module.exports = {
  name: 'subtitles',
  mixins: [FileMixin],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    cache: {
      rest: 'GET /cache',
      async handler(ctx) {
        try {
          const details = {
            subtitles: {count: 0, size: 0, files: []},
            videos: {count: 0, size: 0, files: []},
            totalCount: 0,
            totalSize: 0,
          };

          const subtitlePath = this.getDirPath(STORAGE.SUBTITLES);
          const subtitleFiles = fs.readdirSync(subtitlePath);
          subtitleFiles.forEach(file => {
            const filePath = path.join(subtitlePath, file);
            const fileSize = getFileSize(filePath);
            details.subtitles.count++;
            details.subtitles.size += fileSize;
            details.subtitles.files.push({name: file, size: fileSize});
          });


          const videoPath = this.getDirPath(STORAGE.VIDEOS);
          const videoFiles = fs.readdirSync(videoPath);
          videoFiles.forEach(file => {
            const filePath = path.join(videoPath, file);
            const fileSize = getFileSize(filePath);
            details.videos.count++;
            details.videos.size += fileSize;
            details.videos.files.push({name: file, size: fileSize});
          });


          // Calculate totals
          details.totalCount = details.subtitles.count + details.videos.count;
          details.totalSize = details.subtitles.size + details.videos.size;

          // Format sizes for human readability
          details.subtitles.formattedSize = this.formatBytes(details.subtitles.size);
          details.videos.formattedSize = this.formatBytes(details.videos.size);
          details.formattedTotalSize = this.formatBytes(details.totalSize);

          return details;
        } catch (error) {
          console.error('Error getting cache info:', error.message);
        }
      },
    },
    clearCache: {
      rest: 'DELETE /cache',
      async handler(ctx) {
        try {
          const subtitlePath = this.getDirPath(STORAGE.SUBTITLES);
          const subtitleFiles = fs.readdirSync(subtitlePath);
          subtitleFiles.forEach(file => {
            const filePath = path.join(subtitlePath, file);
            const fileSize = this.getFileSize(filePath);
            details.subtitles.count++;
            details.subtitles.size += fileSize;
            details.subtitles.files.push({name: file, size: fileSize});
            fs.unlinkSync(filePath);
          });

          // Clear videos directory
          const videoPath = this.getDirPath(STORAGE.VIDEOS);
          const videoFiles = fs.readdirSync(videoPath);
          videoFiles.forEach(file => {
            const filePath = path.join(videoPath, file);
            const fileSize = getFileSize(filePath);
            details.videos.count++;
            details.videos.size += fileSize;
            details.videos.files.push({name: file, size: fileSize});
            fs.unlinkSync(filePath);
          });
          // Calculate totals
          details.totalCount = details.subtitles.count + details.videos.count;
          details.totalSize = details.subtitles.size + details.videos.size;

          // Format sizes for human readability
          details.subtitles.formattedSize = this.formatBytes(details.subtitles.size);
          details.videos.formattedSize = formatBytes(details.videos.size);
          details.formattedTotalSize = formatBytes(details.totalSize);


          return {message: 'Cache clear succcessfully'};
        } catch (error) {
          console.error('Error clearing cache:', error);
        }
      },
    },

    // create: {
    //   rest: 'POST /create',
    //   params: {
    //     videoId: 'string',
    //     subtitles: 'string',
    //   },
    //   async handler(ctx) {
    //     const {videoId, subtitles} = ctx.params;
    //     return this.adapter.insert({videoId, subtitles});
    //
    //   },
    // },

    cacheExists: {
      rest: 'GET /exists/:cacheId',
      async handler(ctx) {
        const {cacheId} = req.params;
        const subtitlePath = path.join(SUBTITLES_DIR, `${cacheId}.json`);

        if (fs.existsSync(subtitlePath)) {
          console.log(`Cached subtitles found for ${cacheId}`);
          try {
            const subtitlesData = JSON.parse(fs.readFileSync(subtitlePath, 'utf8'));
            res.json({
              exists: true,
              subtitles: subtitlesData,
            });
          } catch (error) {
            console.error('Error reading cached subtitles:', error);
            res.json({exists: false});
          }
        } else {
          console.log(`No cached subtitles found for ${cacheId}`);
          res.json({exists: false});
        }
      },
    },
    videoExists: {
      rest: 'GET /video-exists/:videoId',
      async handler(ctx) {
        const {videoId} = ctx.params;

        const videoPath = this.getFilePath(this.getDirPath(STORAGE.Videos), `${videoId}.mp4`);

        if (fs.existsSync(videoPath)) {
          const stats = fs.statSync(videoPath);
          res.json({
            exists: true,
            url: `/videos/${videoId}.mp4`,
            size: stats.size,
            createdAt: stats.birthtime,
          });
        } else {
          res.json({exists: false});
        }
      },
    },
    segmentExists: {
      rest: 'GET /segment-exists/:segmentId',
      async handler(ctx) {
        const {segmentId} = req.params;
        const segmentPath = path.join(VIDEOS_DIR, `${segmentId}.mp4`);

        if (fs.existsSync(segmentPath)) {
          const stats = fs.statSync(segmentPath);
          res.json({
            exists: true,
            url: `/videos/${segmentId}.mp4`,
            size: stats.size,
            createdAt: stats.birthtime,
          });
        } else {
          res.json({exists: false});
        }
      },
    },
    downloadVideo: {
      rest: 'POST /download-video',
      async handler(ctx) {
        const {videoId} = ctx.params;

        if (!videoId) {
          throw new MoleculerClientError('Video ID is required');
        }

        const videoPath = this.getFilePath(`${videoId}.mp4`, this.getDirPath(STORAGE.VIDEOS, storageDir));

        if (fs.existsSync(videoPath)) {
          return {success: true, message: 'Video already downloaded'};
        }

        try {
          const result = await this.downloadYouTubeVideo(videoId, videoPath);

          return {message: result.message || 'Video downloaded successfully'};
        } catch (error) {
          console.error('Error downloading video:', error);

          if (fs.existsSync(videoPath)) {
            fs.unlinkSync(videoPath);
          }
        }
      },
    },
    uploadandSpilotVideo: {
      rest: 'POST /upload-and-split-video',
      async handler(ctx) {
        try {
          // Determine if this is a video or audio file based on MIME type
          const contentType = req.headers['content-type'] || '';
          const isAudio = contentType.startsWith('audio/');
          const mediaType = isAudio ? 'audio' : 'video';

          // Generate a unique filename
          const timestamp = Date.now();
          const fileExtension = contentType.split('/')[1] || (isAudio ? 'mp3' : 'mp4');
          const filename = `upload_${timestamp}.${fileExtension}`;
          const mediaPath = path.join(VIDEOS_DIR, filename);

          // Save the uploaded media file
          fs.writeFileSync(mediaPath, req.body);
          console.log(`${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} saved to ${mediaPath}`);

          // Get segment duration from query params or use default (10 minutes)
          const segmentDuration = parseInt(req.query.segmentDuration || '600');
          const fastSplit = req.query.fastSplit === 'true';

          // Split the media file into segments
          const result = await splitMediaIntoSegments(
            mediaPath,
            segmentDuration,
            VIDEOS_DIR,
            `segment_${timestamp}`,
            {
              fastSplit,
              mediaType,
            },
          );

          // Return the list of segment files
          res.json({
            success: true,
            originalMedia: `/videos/${filename}`,
            mediaType: mediaType,
            batchId: result.batchId,
            segments: result.segments.map(segment => `/videos/${path.basename(segment.path)}`),
            message: `${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} uploaded and split successfully`,
          });
        } catch (error) {
          console.error(`Error processing ${mediaType} upload:`, error);
          res.status(500).json({
            success: false,
            error: error.message || `Failed to process ${mediaType}`,
          });
        }
      },
    },
    splitVideo: {
      rest: 'POST /split-video',
      async handler(ctx) {
        try {
          const contentType = req.headers['content-type'] || '';
          const isAudio = contentType.startsWith('audio/');
          const mediaType = isAudio ? 'audio' : 'video';

          // Generate a unique filename for the original media file
          const mediaId = req.query.mediaId || req.query.videoId || `${mediaType}_${Date.now()}`;
          const fileExtension = contentType.split('/')[1] || (isAudio ? 'mp3' : 'mp4');
          const filename = `${mediaId}.${fileExtension}`;
          const mediaPath = path.join(VIDEOS_DIR, filename);


          fs.writeFileSync(mediaPath, req.body);
          console.log(`${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} saved to ${mediaPath}`);

          // Get segment duration from query params or use default (10 minutes = 600 seconds)
          const segmentDuration = parseInt(req.query.segmentDuration || '600');
          const fastSplit = req.query.fastSplit === 'true';

          // Split the media file into segments
          const result = await this.splitMediaIntoSegments(
            mediaPath,
            segmentDuration,
            VIDEOS_DIR,
            `${mediaId}_part`,
            {
              fastSplit,
              mediaType,
            },
          );

          // Return the list of segment files with actual durations
          res.json({
            success: true,
            originalMedia: `/videos/${filename}`,
            mediaId: mediaId,
            mediaType: mediaType,
            segments: result.segments.map(segment => ({
              path: `/videos/${path.basename(segment.path)}`,
              // Include actual duration and start time in the URL as query parameters
              url: `${SERVER_URL}/videos/${path.basename(segment.path)}?startTime=${segment.startTime}&duration=${segment.duration}`,
              name: path.basename(segment.path),
              // Include actual duration and start time in the segment object
              startTime: segment.startTime,
              duration: segment.duration,
              // Include theoretical values for reference
              theoreticalStartTime: segment.theoreticalStartTime,
              theoreticalDuration: segment.theoreticalDuration,
            })),
            message: `${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} split successfully`,
          });
        } catch (error) {
          console.error(`Error splitting ${mediaType}:`, error);
          res.status(500).json({
            success: false,
            error: error.message || `Failed to split ${mediaType}`,
          });
        }
      },
    },
  },

  methods: {
    getMediaDuration(mediaPath) {
      return new Promise((resolve, reject) => {
        const durationProbe = spawn('ffprobe', [
          '-v', 'error',
          '-show_entries', 'format=duration',
          '-of', 'default=noprint_wrappers=1:nokey=1',
          mediaPath,
        ]);

        let durationOutput = '';

        durationProbe.stdout.on('data', (data) => {
          durationOutput += data.toString();
        });

        durationProbe.on('close', (code) => {
          if (code !== 0) {
            return reject(new Error(`Failed to get media duration for ${mediaPath}`));
          }

          const duration = parseFloat(durationOutput.trim());
          resolve(duration);
        });

        durationProbe.stderr.on('data', (data) => {
          console.error(`ffprobe stderr: ${data}`);
        });

        durationProbe.on('error', (err) => {
          reject(err);
        });
      });
    },
    splitMediaIntoSegments(mediaPath, segmentDuration, outputDir, filePrefix, options = {}) {
      // Default to video if mediaType not specified
      const mediaType = options.mediaType || 'video';
      const isAudio = mediaType === 'audio';
      const outputExtension = isAudio ? 'mp3' : 'mp4';
      return new Promise((resolve, reject) => {
        const timestamp = Date.now();
        const safePrefix = filePrefix.replace(/[^a-zA-Z0-9-_]/g, '_').slice(0, 20);
        const batchId = `${safePrefix}_${timestamp}`;

        // First, get the duration of the media file
        const durationProbe = spawn('ffprobe', [
          '-v', 'error',
          '-show_entries', 'format=duration',
          '-of', 'default=noprint_wrappers=1:nokey=1',
          mediaPath,
        ]);

        let durationOutput = '';

        durationProbe.stdout.on('data', (data) => {
          durationOutput += data.toString();
        });

        durationProbe.on('close', async (code) => {
          if (code !== 0) {
            return reject(new Error(`Failed to get ${mediaType} duration`));
          }

          const totalDuration = parseFloat(durationOutput.trim());
          console.log(`${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)} duration: ${totalDuration} seconds`);

          const numSegments = Math.ceil(totalDuration / segmentDuration);
          console.log(`Splitting ${mediaType} into ${numSegments} segments of ${segmentDuration} seconds each`);

          const outputPattern = path.join(outputDir, `${batchId}_%03d.${outputExtension}`);

          // Construct ffmpeg command based on splitting mode
          const ffmpegArgs = [
            '-i', mediaPath,
            '-f', 'segment',
            '-segment_time', segmentDuration.toString(),
            '-reset_timestamps', '1',
          ];

          // If fast split is enabled, use stream copy instead of re-encoding
          if (options.fastSplit) {
            // When using stream copy, we need to be careful about keyframes
            // Add segment_time_delta to allow some flexibility in segment boundaries
            ffmpegArgs.push(
              '-segment_time_delta', '1.0',  // Allow 1 second flexibility
              '-c', 'copy',
            );
            console.log('Using fast split mode with stream copy');
          } else if (isAudio) {
            // For audio, use high-quality audio encoding
            ffmpegArgs.push(
              '-c:a', 'libmp3lame',
              '-q:a', '2', // High quality (0 is best, 9 is worst)
              '-threads', '0',
            );
            console.log('Using audio encoding mode');
          } else {
            // For video, use high-performance encoding settings
            ffmpegArgs.push(
              '-c:v', 'libx264',
              '-preset', 'faster', // Use faster preset for better performance
              '-c:a', 'aac',
              '-force_key_frames', `expr:gte(t,n_forced*${segmentDuration})`, // Force keyframe at segment boundaries
              '-threads', '0',
            );
            console.log('Using re-encoding mode with forced keyframes');
          }

          ffmpegArgs.push(outputPattern);

          const segmentCmd = spawn('ffmpeg', ffmpegArgs);
          const segments = [];

          segmentCmd.stderr.on('data', (data) => {
            const output = data.toString();
            if (output.includes('frame=')) {
              process.stdout.write('.');
            }
            console.log('ffmpeg stderr:', output);
          });

          segmentCmd.on('close', async (code) => {
            if (code !== 0) {
              return reject(new Error(`Failed to split ${mediaType}`));
            }

            // Read the created segments
            fs.readdir(outputDir, async (err, files) => {
              if (err) return reject(err);

              const segmentFiles = files
                .filter(file => file.startsWith(batchId))
                .sort((a, b) => {
                  // Use a regex that works for both mp3 and mp4 files
                  const aMatch = a.match(/(\d+)\.(mp[34])$/);
                  const bMatch = b.match(/(\d+)\.(mp[34])$/);
                  if (!aMatch || !bMatch) return 0;
                  const aIndex = parseInt(aMatch[1]);
                  const bIndex = parseInt(bMatch[1]);
                  return aIndex - bIndex;
                });

              // Get actual durations for all segments
              let cumulativeStartTime = 0;
              const segmentPromises = segmentFiles.map(async (file, index) => {
                const filePath = path.join(outputDir, file);
                // Get actual duration using ffprobe
                const actualDuration = await getMediaDuration(filePath);

                // Calculate start time based on previous segments' actual durations
                const startTime = cumulativeStartTime;
                // Update cumulative start time for next segment
                cumulativeStartTime += actualDuration;

                // For the last segment, use the actual duration
                // For other segments, use the actual measured duration
                return {
                  index,
                  path: filePath,
                  url: `/videos/${file}`,
                  startTime,
                  duration: actualDuration,
                  // Keep the theoretical values for reference
                  theoreticalStartTime: index * segmentDuration,
                  theoreticalDuration: index < numSegments - 1 ?
                    segmentDuration :
                    totalDuration - (index * segmentDuration),
                };
              });

              try {
                // Wait for all duration checks to complete
                let segmentResults = await Promise.all(segmentPromises);

                // Log the initial segment information
                console.log('Initial segment information:');
                segmentResults.forEach((segment, i) => {
                  console.log(`Segment ${i} (file index ${segment.index}): startTime=${segment.startTime.toFixed(2)}s, duration=${segment.duration.toFixed(2)}s, endTime=${(segment.startTime + segment.duration).toFixed(2)}s`);
                });

                // Check if segments are in the correct order by start time
                // This is important when using stream-copy mode, which can split at unexpected points
                const isOutOfOrder = segmentResults.some((segment, i) => {
                  if (i === 0) return false;
                  const isWrongOrder = segment.startTime < segmentResults[i - 1].startTime;
                  if (isWrongOrder) {
                    console.log(`Segment order issue detected: Segment ${i} starts at ${segment.startTime.toFixed(2)}s which is before segment ${i - 1} at ${segmentResults[i - 1].startTime.toFixed(2)}s`);
                  }
                  return isWrongOrder;
                });

                if (isOutOfOrder) {
                  console.log('WARNING: Segments are out of order by start time. Reordering...');

                  // Sort segments by their file index first (this should be the intended order)
                  segmentResults.sort((a, b) => a.index - b.index);

                  // Recalculate start times based on the correct order
                  let newCumulativeStartTime = 0;
                  segmentResults = segmentResults.map((segment, i) => {
                    const newStartTime = newCumulativeStartTime;
                    newCumulativeStartTime += segment.duration;

                    return {
                      ...segment,
                      startTime: newStartTime,
                      // Update theoretical values too
                      theoreticalStartTime: i * segmentDuration,
                      theoreticalDuration: i < numSegments - 1 ?
                        segmentDuration :
                        totalDuration - (i * segmentDuration),
                    };
                  });

                  // Log the reordered segment information
                  console.log('Segments reordered by file index. New segment information:');
                  segmentResults.forEach((segment, i) => {
                    console.log(`Segment ${i} (file index ${segment.index}): startTime=${segment.startTime.toFixed(2)}s, duration=${segment.duration.toFixed(2)}s, endTime=${(segment.startTime + segment.duration).toFixed(2)}s`);
                  });
                }

                segments.push(...segmentResults);
                resolve({segments, batchId});
              } catch (error) {
                reject(new Error(`Failed to get segment durations: ${error.message}`));
              }
            });
          });

          segmentCmd.stderr.on('data', (data) => {
            console.error(`ffmpeg stderr: ${data}`);
          });

          segmentCmd.on('error', (err) => {
            reject(err);
          });
        });

        durationProbe.stderr.on('data', (data) => {
          console.error(`ffprobe stderr: ${data}`);
        });

        durationProbe.on('error', (err) => {
          reject(err);
        });
      });
    },
    splitVideoIntoSegments(videoPath, segmentDuration, outputDir, filePrefix, options = {}) {
      return this.splitMediaIntoSegments(videoPath, segmentDuration, outputDir, filePrefix, {
        ...options,
        mediaType: 'video',
      });
    },
    async isFFmpegAvailable() {
      try {
        await exec('ffmpeg -version');
        return true;
      } catch (error) {
        console.warn('FFmpeg is not available:', error.message);
        return false;
      }
    },
    async mergeVideoAndAudio(videoPath, audioPath, outputPath) {
      try {
        console.log(`[QUALITY DEBUG] Merging video and audio files:`);
        console.log(`[QUALITY DEBUG] Video: ${videoPath}`);
        console.log(`[QUALITY DEBUG] Audio: ${audioPath}`);
        console.log(`[QUALITY DEBUG] Output: ${outputPath}`);

        // Use ffmpeg to merge the files
        const command = `ffmpeg -i "${videoPath}" -i "${audioPath}" -c:v copy -c:a aac -strict experimental "${outputPath}" -y`;
        console.log(`[QUALITY DEBUG] Running command: ${command}`);

        const {stdout, stderr} = await exec(command);

        if (stderr) {
          console.log(`[QUALITY DEBUG] FFmpeg stderr: ${stderr}`);
        }

        // Check if the output file exists
        if (fs.existsSync(outputPath)) {
          console.log(`[QUALITY DEBUG] Merge successful, output file exists`);
          return true;
        } else {
          console.error(`[QUALITY DEBUG] Merge failed, output file does not exist`);
          return false;
        }
      } catch (error) {
        console.error(`[QUALITY DEBUG] Error merging files:`, error);
        return false;
      }
    },
    async downloadAndMerge(videoURL, outputPath, videoFormat, audioFormat) {

      const ffmpegAvailable = await this.isFFmpegAvailable();
      if (!ffmpegAvailable) {
        return false;
      }

      // Create temporary file paths
      const tempVideoPath = `${outputPath}.video.tmp`;
      const tempAudioPath = `${outputPath}.audio.tmp`;

      try {
        console.log(`[QUALITY DEBUG] Downloading video and audio separately:`);
        console.log(`[QUALITY DEBUG] Video format: ${videoFormat.qualityLabel || videoFormat.quality}`);
        console.log(`[QUALITY DEBUG] Audio format: ${audioFormat.qualityLabel || 'audio only'}`);

        // Download video
        const videoStream = ytdl(videoURL, {format: videoFormat});

        // Download audio
        const audioStream = ytdl(videoURL, {format: audioFormat});
        const audioWriteStream = fs.createWriteStream(tempAudioPath);

        const mergeSuccess = await this.mergeVideoAndAudio(tempVideoPath, tempAudioPath, outputPath);

        try {
          if (fs.existsSync(tempVideoPath)) {
            await fsPromises.unlink(tempVideoPath);
          }
          if (fs.existsSync(tempAudioPath)) {
            await fsPromises.unlink(tempAudioPath);
          }
        } catch (cleanupError) {
          console.warn(`[QUALITY DEBUG] Error cleaning up temporary files:`, cleanupError);
        }

        return mergeSuccess;
      } catch (error) {
        console.error(`[QUALITY DEBUG] Error in downloadAndMerge:`, error);

        // Clean up temporary files in case of error
        try {
          if (fs.existsSync(tempVideoPath)) {
            await fsPromises.unlink(tempVideoPath);
          }
          if (fs.existsSync(tempAudioPath)) {
            await fsPromises.unlink(tempAudioPath);
          }
        } catch (cleanupError) {
          console.warn(`[QUALITY DEBUG] Error cleaning up temporary files:`, cleanupError);
        }

        return false;
      }
    },
    async downloadYouTubeVideo(videoId, outputPath, quality = '360p') {
      const videoURL = `https://www.youtube.com/watch?v=${videoId}`;

      let videoInfo;
      try {
        videoInfo = await this.getVideoInfo(videoId);
      } catch (error) {
        return;
      }
      // Special handling for 360p to ensure we get audio
      if (quality === '360p') {
        try {
          const success = await this.download360pWithAudio(videoURL, outputPath);

          if (success) {
            return {
              success: true,
              path: outputPath,
              message: `Video downloaded successfully with 360p special method`,
              title: videoInfo.title,
              method: '360p-special',
            };
          }
        } catch (error) {
          console.error(`Error with special 360p download: ${error.message}`);
          console.log(`Falling back to standard methods...`);
        }
      }

      // Try multiple download methods in sequence
      // const methods = [
      //   {name: 'ytdl-core', fn: this.downloadWithYtdlCore},
      //   {name: 'play-dl', fn: this.downloadWithPlayDl},
      //   {name: 'direct-stream', fn: this.downloadWithDirectStream},
      // ];
      //
      // let lastError = null;
      //
      // for (const method of methods) {
      //   try {
      //     console.log(`Attempting to download with ${method.name}...`);
      //     const result = await method.fn(videoURL, outputPath, quality);
      //     console.log(`Successfully downloaded video with ${method.name}`);
      //
      //     return {
      //       success: true,
      //       path: outputPath,
      //       message: `Video downloaded successfully with ${method.name}`,
      //       title: videoInfo.title,
      //       method: method.name,
      //     };
      //   } catch (error) {
      //     console.error(`${method.name} download failed:`, error.message);
      //     lastError = error;
      //   }
      // }
      //
      // // If we get here, all methods failed
      // throw new Error(`All download methods failed. Last error: ${lastError?.message}`);
    },
    getVideoInfo(videoId) {
      return new Promise((resolve, reject) => {
        const url = `https://www.youtube.com/oembed?url=https://www.youtube.com/watch?v=${videoId}&format=json`;

        https.get(url, (res) => {
          if (res.statusCode !== 200) {
            reject(new Error(`Failed to get video info: HTTP ${res.statusCode}`));
            return;
          }

          let data = '';
          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', () => {
            try {
              const info = JSON.parse(data);
              resolve({
                title: info.title,
                author: info.author_name,
                thumbnailUrl: info.thumbnail_url,
              });
            } catch (error) {
              reject(new Error(`Failed to parse video info: ${error.message}`));
            }
          });
        }).on('error', (error) => {
          reject(new Error(`Failed to fetch video info: ${error.message}`));
        });
      });
    },
    async downloadWithYtdlCore(videoURL, outputPath, quality = '360p') {
      return new Promise(async (resolve, reject) => {
        try {
          // Convert quality string to itag or quality level
          let qualityOption;

          // Map quality string to appropriate ytdl-core quality option
          switch (quality) {
            case '144p':
              qualityOption = 'lowest';
              break;
            case '240p':
              qualityOption = 'lowest';
              break;
            case '360p':
              qualityOption = 'medium';
              break;
            case '480p':
              qualityOption = 'medium';
              break;
            case '720p':
              qualityOption = 'highest';
              break;
            default:
              qualityOption = 'medium'; // Default to medium quality
          }

          console.log(`[QUALITY DEBUG] Using ytdl-core quality option: ${qualityOption} for requested quality: ${quality}`);

          // Set up ytdl options for the specified quality that includes both video and audio
          let options;

          // For all qualities, we'll use a more specific approach with format filtering
          // This helps avoid the 'No such format found: medium' error
          const targetHeight = {
            '144p': 144,
            '240p': 240,
            '360p': 360,
            '480p': 480,
            '720p': 720,
            '1080p': 1080,
          }[quality] || 360;

          console.log(`[QUALITY DEBUG] Target height for ${quality}: ${targetHeight}px`);

          // Don't use qualityOption (like 'medium') at all - it's causing the error
          // Instead, use a filter function that selects formats based on height
          options = {
            filter: format => {
              // For 360p specifically, we need to prioritize formats with audio
              if (quality === '360p') {
                if (format.container === 'mp4' && format.hasVideo && format.hasAudio && format.height === 360) {
                  return true;
                }

                // Second priority: close to 360p with audio
                if (format.container === 'mp4' && format.hasVideo && format.hasAudio &&
                  format.height >= 360 && format.height <= 480) {
                  return true;
                }

                // Last resort: 360p without audio (will need separate audio download)
                return format.container === 'mp4' && format.hasVideo &&
                  (format.height === 360 || (format.height >= 360 && format.height <= 480));
              }

              // For other qualities, use the standard height filter
              return format.container === 'mp4' &&
                format.hasVideo &&
                format.height <= targetHeight;
            },
          };

          console.log(`[QUALITY DEBUG] Using height-based filter for ${quality}`);

          // Create write stream
          const writeStream = fs.createWriteStream(outputPath);

          // Set up event handlers
          writeStream.on('finish', () => {
            console.log(`ytdl-core download successful: ${outputPath}`);
            resolve(true);
          });

          writeStream.on('error', (err) => {
            console.error('Write stream error:', err);
            reject(err);
          });

          // Start download
          // Get video info to check available formats
          const info = await ytdl.getInfo(videoURL);
          console.log(`[QUALITY DEBUG] Video title: ${info.videoDetails.title}`);

          // For 144p, 240p, and 360p, try to find a specific format
          let videoStream;
          if (quality === '360p') {
            // Log all available formats for debugging
            console.log(`[QUALITY DEBUG] Available formats:`);
            info.formats.forEach(format => {
              if (format.height) {
                console.log(`[QUALITY DEBUG] Format: ${format.qualityLabel}, Height: ${format.height}, Container: ${format.container}, Has Audio: ${format.hasAudio}`);
              }
            });

            // First, look for 360p formats that already have audio
            const formatsWithAudio = info.formats.filter(format =>
              format.hasVideo &&
              format.hasAudio &&
              format.height === 360 &&
              format.container === 'mp4',
            );

            if (formatsWithAudio.length > 0) {
              console.log(`[QUALITY DEBUG] Found ${formatsWithAudio.length} 360p formats with audio`);
              const selectedFormat = formatsWithAudio[0];
              console.log(`[QUALITY DEBUG] Using 360p format with audio: ${selectedFormat.qualityLabel}`);
              videoStream = ytdl(videoURL, {format: selectedFormat});
            } else {
              console.log(`[QUALITY DEBUG] No 360p formats with audio found, using filter`);
              videoStream = ytdl(videoURL, options);
            }
          } else if (quality === '144p' || quality === '240p') {
            const targetHeight = quality === '144p' ? 144 : 240;
            console.log(`[QUALITY DEBUG] Looking for format with height <= ${targetHeight}`);

            // Log all available formats for debugging
            console.log(`[QUALITY DEBUG] Available formats:`);
            info.formats.forEach(format => {
              if (format.hasVideo && format.height) {
                console.log(`[QUALITY DEBUG] Format: ${format.qualityLabel}, Height: ${format.height}, Container: ${format.container}, Has Audio: ${format.hasAudio}`);
              }
            });

            // Find formats with the target height or lower
            // For 144p, we need to be very specific
            let formats;
            if (quality === '144p') {
              formats = info.formats.filter(format =>
                format.hasVideo &&
                format.height &&
                format.height <= targetHeight,
              );

              // Sort by height (descending)
              formats.sort((a, b) => b.height - a.height);

              // If we found formats, take the highest one within our limit
              if (formats.length > 0) {
                console.log(`[QUALITY DEBUG] Found ${formats.length} formats for 144p, using height ${formats[0].height}`);
              }
            } else {
              // For other qualities, require mp4 container and audio
              formats = info.formats.filter(format =>
                format.hasVideo &&
                format.hasAudio &&
                format.container === 'mp4' &&
                format.height &&
                format.height <= targetHeight,
              );
            }

            // Sort by height (descending) to get the highest quality within our limit
            formats.sort((a, b) => b.height - a.height);

            if (formats.length > 0) {
              console.log(`[QUALITY DEBUG] Found specific format with height ${formats[0].height}, has audio: ${formats[0].hasAudio}`);

              // For low quality formats, we often need to handle formats without audio
              if (!formats[0].hasAudio) {
                console.log(`[QUALITY DEBUG] Selected format doesn't have audio, using special handling`);

                // Get an audio-only format
                const audioFormats = info.formats.filter(format =>
                  format.hasAudio && !format.hasVideo,
                );

                if (audioFormats.length > 0) {
                  console.log(`[QUALITY DEBUG] Found audio format, will download video and audio separately`);

                  // Sort audio formats by bitrate (descending)
                  audioFormats.sort((a, b) => (b.audioBitrate || 0) - (a.audioBitrate || 0));

                  console.log(`[QUALITY DEBUG] Selected audio format with bitrate: ${audioFormats[0].audioBitrate}kbps`);

                  // Use our downloadAndMerge function to handle this case
                  const downloadSuccess = await downloadAndMerge(videoURL, outputPath, formats[0], audioFormats[0]);

                  if (downloadSuccess) {
                    console.log(`[QUALITY DEBUG] Successfully downloaded and merged video and audio`);
                    resolve(true);
                    return; // Exit the function early since we've handled everything
                  } else {
                    console.log(`[QUALITY DEBUG] Failed to download and merge, falling back to video-only format`);
                    videoStream = ytdl(videoURL, {format: formats[0]});
                  }
                } else {
                  console.log(`[QUALITY DEBUG] No audio formats found, using video-only format`);
                  videoStream = ytdl(videoURL, {format: formats[0]});
                }
              } else {
                // Use the specific format that already has audio
                console.log(`[QUALITY DEBUG] Selected format has audio, using it directly`);
                videoStream = ytdl(videoURL, {format: formats[0]});
              }
            } else {
              console.log(`[QUALITY DEBUG] No specific format found for height <= ${targetHeight}, falling back to options`);
              videoStream = ytdl(videoURL, options);
            }
          } else if (quality === '360p') {
            // For 360p, we want to prioritize formats with audio
            // First, look for formats that have both video and audio
            const formatsWithAudio = info.formats.filter(format =>
              format.hasVideo &&
              format.hasAudio &&
              format.height === 360 &&
              format.container === 'mp4',
            );

            if (formatsWithAudio.length > 0) {
              console.log(`[QUALITY DEBUG] Found ${formatsWithAudio.length} 360p formats with audio`);
              const selectedFormat = formatsWithAudio[0];
              console.log(`[QUALITY DEBUG] Using 360p format with audio: ${selectedFormat.qualityLabel}`);
              videoStream = ytdl(videoURL, {format: selectedFormat});
            } else {
              // If no 360p format with audio, try formats close to 360p with audio
              const nearbyFormatsWithAudio = info.formats.filter(format =>
                format.hasVideo &&
                format.hasAudio &&
                format.height >= 360 &&
                format.height <= 480 &&
                format.container === 'mp4',
              );

              if (nearbyFormatsWithAudio.length > 0) {
                // Sort by height (closest to 360p first)
                nearbyFormatsWithAudio.sort((a, b) => Math.abs(a.height - 360) - Math.abs(b.height - 360));
                const selectedFormat = nearbyFormatsWithAudio[0];
                console.log(`[QUALITY DEBUG] Using nearby format with audio: ${selectedFormat.qualityLabel}`);
                videoStream = ytdl(videoURL, {format: selectedFormat});
              } else {
                // If no formats with audio, use the standard options and let the downloadAndMerge logic handle it
                console.log(`[QUALITY DEBUG] No 360p formats with audio found, using standard options`);
                videoStream = ytdl(videoURL, options);
              }
            }
          } else {
            // Use the standard options
            videoStream = ytdl(videoURL, options);
          }

          videoStream.on('error', (err) => {
            console.error('ytdl-core stream error:', err);
            writeStream.end();
            reject(err);
          });

          // Log progress
          videoStream.on('progress', (chunkLength, downloaded, total) => {
            const percent = downloaded / total * 100;
            console.log(`ytdl-core download progress: ${percent.toFixed(2)}%`);
          });

          // Pipe the video stream to the file
          videoStream.pipe(writeStream);
        } catch (err) {
          console.error('ytdl-core setup error:', err);
          reject(err);
        }
      });
    },
    async downloadWithPlayDl(videoURL, outputPath, quality = '360p') {
      try {
        // Convert quality string to play-dl quality number
        let qualityOption;

        // Map quality string to appropriate play-dl quality option
        switch (quality) {
          case '144p':
            qualityOption = 144;
            break;
          case '240p':
            qualityOption = 240;
            break;
          case '360p':
            qualityOption = 360;
            break;
          case '480p':
            qualityOption = 480;
            break;
          case '720p':
            qualityOption = 720;
            break;
          default:
            qualityOption = 360; // Default to 360p
        }

        console.log(`[QUALITY DEBUG] Using play-dl quality option: ${qualityOption} for requested quality: ${quality}`);

        // Get video info to check available qualities
        const videoInfo = await playDl.video_info(videoURL);
        console.log(`[QUALITY DEBUG] Video title: ${videoInfo.video_details.title}`);

        // Log available formats for debugging
        console.log(`[QUALITY DEBUG] Available formats from play-dl:`);
        try {
          const formats = await playDl.video_basic_info(videoURL);
          if (formats && formats.video_details && formats.video_details.formats) {
            formats.video_details.formats.forEach(format => {
              if (format.qualityLabel) {
                console.log(`[QUALITY DEBUG] Format: ${format.qualityLabel}, mimeType: ${format.mimeType}`);
              }
            });
          } else {
            console.log(`[QUALITY DEBUG] No formats available from play-dl or format structure is different than expected`);
          }
        } catch (error) {
          console.log(`[QUALITY DEBUG] Error getting formats from play-dl: ${error.message}`);
        }

        // For low quality formats, try to use ytdl-core with our downloadAndMerge function
        // since play-dl doesn't give us as much control over format selection
        if (quality === '144p' || quality === '240p') {
          try {
            console.log(`[QUALITY DEBUG] For ${quality}, trying to use ytdl-core with downloadAndMerge`);

            // Get video info from ytdl-core
            const ytdlInfo = await ytdl.getInfo(videoURL);

            // Find video format with the target height
            const targetHeight = quality === '144p' ? 144 : 240;
            const videoFormats = ytdlInfo.formats.filter(format =>
              format.hasVideo &&
              format.height &&
              format.height <= targetHeight,
            );

            // Sort by height (descending)
            videoFormats.sort((a, b) => b.height - a.height);

            if (videoFormats.length > 0) {
              console.log(`[QUALITY DEBUG] Found ${videoFormats.length} video formats for ${quality}`);

              // Get audio formats
              const audioFormats = ytdlInfo.formats.filter(format =>
                format.hasAudio && !format.hasVideo,
              );

              // Sort by audio bitrate
              audioFormats.sort((a, b) => (b.audioBitrate || 0) - (a.audioBitrate || 0));

              if (audioFormats.length > 0) {
                console.log(`[QUALITY DEBUG] Found ${audioFormats.length} audio formats`);

                // Try to download and merge
                const downloadSuccess = await downloadAndMerge(videoURL, outputPath, videoFormats[0], audioFormats[0]);

                if (downloadSuccess) {
                  console.log(`[QUALITY DEBUG] Successfully downloaded and merged video and audio`);
                  return true; // Exit the function early since we've handled everything
                } else {
                  console.log(`[QUALITY DEBUG] Failed to download and merge, falling back to play-dl`);
                }
              }
            }
          } catch (error) {
            console.error(`[QUALITY DEBUG] Error trying to use ytdl-core:`, error);
            console.log(`[QUALITY DEBUG] Falling back to play-dl`);
          }
        }

        // If we get here, either we're not trying to get a low quality format,
        // or the ytdl-core approach failed, so we'll use play-dl

        // Get stream with play-dl
        let streamOptions;

        // For 360p specifically, we need a different approach
        if (quality === '360p') {
          // Try a special approach for 360p that has been problematic
          try {
            // Try to download with ytdl-core directly using a specific filter that prioritizes formats with audio
            console.log(`[QUALITY DEBUG] For 360p, trying direct ytdl-core download with audio`);

            // Get video info first to check available formats
            const ytdlInfo = await ytdl.getInfo(videoURL);

            // Log available formats for debugging
            console.log(`[QUALITY DEBUG] Available formats:`);
            ytdlInfo.formats.forEach(format => {
              if (format.height) {
                console.log(`[QUALITY DEBUG] Format: ${format.qualityLabel}, Height: ${format.height}, Container: ${format.container}, Has Audio: ${format.hasAudio}`);
              }
            });

            // First, look for 360p formats that already have audio
            const formatsWithAudio = ytdlInfo.formats.filter(format =>
              format.hasVideo &&
              format.hasAudio &&
              format.height === 360 &&
              format.container === 'mp4',
            );

            if (formatsWithAudio.length > 0) {
              console.log(`[QUALITY DEBUG] Found ${formatsWithAudio.length} 360p formats with audio`);

              return new Promise((resolve, reject) => {
                try {
                  // Use the first format that has both video and audio
                  const selectedFormat = formatsWithAudio[0];
                  console.log(`[QUALITY DEBUG] Using 360p format with audio: ${selectedFormat.qualityLabel}`);

                  // Create the stream with the specific format
                  const videoStream = ytdl(videoURL, {format: selectedFormat});
                  const writeStream = fs.createWriteStream(outputPath);

                  // Set up event handlers
                  writeStream.on('finish', () => {
                    console.log(`ytdl-core direct download successful for 360p with audio: ${outputPath}`);
                    resolve(true);
                  });

                  writeStream.on('error', (err) => {
                    console.error(`ytdl-core write stream error: ${err.message}`);
                    reject(err);
                  });

                  videoStream.on('error', (err) => {
                    console.error(`ytdl-core stream error: ${err.message}`);
                    reject(err);
                  });

                  // Pipe the stream to the file
                  videoStream.pipe(writeStream);
                } catch (err) {
                  console.error(`Error setting up ytdl-core stream: ${err.message}`);
                  reject(err);
                }
              });
            } else {
              console.log(`[QUALITY DEBUG] No 360p formats with audio found, falling back to filter approach`);

              return new Promise((resolve, reject) => {
                try {
                  // Create a ytdl stream with specific options for 360p
                  const ytdlOptions = {
                    filter: format => {
                      return format.container === 'mp4' &&
                        format.hasVideo &&
                        format.height >= 360 &&
                        format.height <= 480;
                    },
                  };

                  console.log(`[QUALITY DEBUG] Using specific ytdl-core filter for 360p`);

                  // Create the stream
                  const videoStream = ytdl(videoURL, ytdlOptions);
                  const writeStream = fs.createWriteStream(outputPath);

                  // Set up event handlers
                  writeStream.on('finish', () => {
                    console.log(`ytdl-core direct download successful for 360p: ${outputPath}`);
                    resolve(true);
                  });

                  writeStream.on('error', (err) => {
                    console.error(`ytdl-core write stream error: ${err.message}`);
                    reject(err);
                  });

                  videoStream.on('error', (err) => {
                    console.error(`ytdl-core stream error: ${err.message}`);
                    reject(err);
                  });

                  // Pipe the stream to the file
                  videoStream.pipe(writeStream);
                } catch (err) {
                  console.error(`Error setting up ytdl-core stream: ${err.message}`);
                  reject(err);
                }
              });
            }
          } catch (error) {
            console.log(`[QUALITY DEBUG] Error with direct ytdl-core approach for 360p: ${error.message}`);
            // Continue to the next approach
          }

          // If we get here, the direct ytdl-core approach failed
          // We'll skip the downloadAndMerge approach for 360p since we want to prioritize formats with audio
          // and only use downloadAndMerge as a last resort
          console.log(`[QUALITY DEBUG] Direct ytdl-core approach failed, skipping downloadAndMerge for 360p and trying play-dl`);

          // If we get here, the ytdl-core approach failed, so use play-dl with a specific quality
          // For 360p, we want to make sure we get a format with audio
          streamOptions = {
            quality: 360,
            filter: 'audioandvideo', // This ensures we get a format with both audio and video
          };
          console.log(`[QUALITY DEBUG] Using quality: 360 with audioandvideo filter for play-dl`);
        } else if (quality === '144p' || quality === '240p') {
          // For low qualities, force the lowest possible quality
          streamOptions = {quality: 'lowest'};
          console.log(`[QUALITY DEBUG] Using 'lowest' quality option for play-dl to get ${quality}`);
        } else {
          // For other qualities, use the standard quality option
          streamOptions = {quality: qualityOption};
          console.log(`[QUALITY DEBUG] Using standard quality option for play-dl: ${qualityOption}`);
        }

        // Try to download with play-dl
        return new Promise(async (resolve, reject) => {
          try {
            console.log(`[QUALITY DEBUG] Attempting play-dl download with options:`, streamOptions);
            const stream = await playDl.stream(videoURL, streamOptions);

            // Create write stream
            const writeStream = fs.createWriteStream(outputPath);

            // Set up event handlers
            writeStream.on('finish', () => {
              console.log(`play-dl download successful: ${outputPath}`);
              resolve(true);
            });

            writeStream.on('error', (err) => {
              console.error(`play-dl write stream error: ${err.message}`);
              writeStream.end();
              reject(err);
            });

            stream.stream.on('error', (err) => {
              console.error(`play-dl stream error: ${err.message}`);
              writeStream.end();
              reject(err);
            });

            // Pipe the stream to the file
            stream.stream.pipe(writeStream);
          } catch (error) {
            console.log(`[QUALITY DEBUG] Error getting stream from play-dl: ${error.message}`);

            // Try with a different approach as fallback
            try {
              console.log(`[QUALITY DEBUG] Trying with default options as fallback`);
              const fallbackStream = await playDl.stream(videoURL);

              // Create write stream
              const writeStream = fs.createWriteStream(outputPath);

              // Set up event handlers
              writeStream.on('finish', () => {
                console.log(`play-dl fallback download successful: ${outputPath}`);
                resolve(true);
              });

              writeStream.on('error', (err) => {
                console.error(`play-dl fallback write stream error: ${err.message}`);
                writeStream.end();
                reject(err);
              });

              fallbackStream.stream.on('error', (err) => {
                console.error(`play-dl fallback stream error: ${err.message}`);
                writeStream.end();
                reject(err);
              });

              // Pipe the stream to the file
              fallbackStream.stream.pipe(writeStream);
            } catch (fallbackError) {
              console.error(`play-dl fallback error: ${fallbackError.message}`);
              reject(fallbackError);
            }
          }
        });
      } catch (error) {
        console.error('play-dl error:', error);
        return false;
      }
    },
    async download360pWithAudio(videoURL, outputPath) {
      try {
        let info;
        info = await ytdl.getInfo(videoURL);

        const formatsWithAudio = info.formats.filter(format =>
          format.hasVideo &&
          format.hasAudio &&
          format.height === 360 &&
          format.container === 'mp4',
        );
        if (formatsWithAudio.length > 0) {
          if (!fs.existsSync(outputPath)) {
            const video = ytdl(videoURL, {format: formatsWithAudio[0]});
            return this.saveToLocalStorage(video, outputPath);
          }
        }
        const nearbyFormatsWithAudio = info.formats.filter(format =>
          format.hasVideo &&
          format.hasAudio &&
          format.height >= 360 &&
          format.height <= 480 &&
          format.container === 'mp4',
        );

        if (nearbyFormatsWithAudio.length > 0) {
          nearbyFormatsWithAudio.sort((a, b) => Math.abs(a.height - 360) - Math.abs(b.height - 360));
          const video = ytdl(videoURL, {format: formatsWithAudio[0]});
          return this.saveToLocalStorage(video, outputPath);
        }

        const videoFormats = info.formats.filter(format =>
          format.hasVideo &&
          format.height &&
          format.height >= 360 &&
          format.height <= 480,
        );

        videoFormats.sort((a, b) => Math.abs(a.height - 360) - Math.abs(b.height - 360));

        if (videoFormats.length > 0) {
          // Find the best audio format
          const audioFormats = info.formats.filter(format => format.hasAudio && !format.hasVideo);

          // Sort by audio bitrate
          audioFormats.sort((a, b) => (b.audioBitrate || 0) - (a.audioBitrate || 0));

          if (audioFormats.length > 0) {
            const downloadSuccess = await this.downloadAndMerge(videoURL, outputPath, videoFormats[0], audioFormats[0]);

            if (downloadSuccess) {
              return true;
            }
          }
        }

        return false;
      } catch (error) {
        console.error(error.message);
        return false;
      }
    },
    async downloadWithDirectStream(videoURL, outputPath, quality = '360p') {
      // Note: For direct stream, we can't easily control quality
      // but we log it for consistency
      console.log(`[QUALITY DEBUG] Using direct stream with requested quality: ${quality}`);

      // For low quality formats, try to use ytdl-core with our downloadAndMerge function first
      if (quality === '144p' || quality === '240p') {
        try {
          console.log(`[QUALITY DEBUG] For ${quality}, trying to use ytdl-core with downloadAndMerge`);

          // Get video info from ytdl-core
          const ytdlInfo = await ytdl.getInfo(videoURL);

          // Find video format with the target height
          const targetHeight = quality === '144p' ? 144 : 240;
          const videoFormats = ytdlInfo.formats.filter(format =>
            format.hasVideo &&
            format.height &&
            format.height <= targetHeight,
          );

          // Sort by height (descending)
          videoFormats.sort((a, b) => b.height - a.height);

          if (videoFormats.length > 0) {
            console.log(`[QUALITY DEBUG] Found ${videoFormats.length} video formats for ${quality}`);

            // Get audio formats
            const audioFormats = ytdlInfo.formats.filter(format =>
              format.hasAudio && !format.hasVideo,
            );

            // Sort by audio bitrate
            audioFormats.sort((a, b) => (b.audioBitrate || 0) - (a.audioBitrate || 0));

            if (audioFormats.length > 0) {
              console.log(`[QUALITY DEBUG] Found ${audioFormats.length} audio formats`);

              // Try to download and merge
              const downloadSuccess = await downloadAndMerge(videoURL, outputPath, videoFormats[0], audioFormats[0]);

              if (downloadSuccess) {
                console.log(`[QUALITY DEBUG] Successfully downloaded and merged video and audio`);
                return true; // Exit the function early since we've handled everything
              } else {
                console.log(`[QUALITY DEBUG] Failed to download and merge, falling back to direct stream`);
              }
            }
          }
        } catch (error) {
          console.error(`[QUALITY DEBUG] Error trying to use ytdl-core:`, error);
          console.log(`[QUALITY DEBUG] Falling back to direct stream`);
        }
      }

      // If we get here, either we're not trying to get a low quality format,
      // or the ytdl-core approach failed, so we'll use direct stream
      console.log(`[QUALITY DEBUG] Using direct stream method (quality not directly supported)`);

      return new Promise((resolve, reject) => {
        try {
          // Create a temporary file for the video
          const tempFilePath = `${outputPath}.temp`;
          const writeStream = fs.createWriteStream(tempFilePath);

          // Set up event handlers
          writeStream.on('finish', () => {
            // Rename the temp file to the final output path
            fs.rename(tempFilePath, outputPath, (err) => {
              if (err) {
                console.error('Error renaming temp file:', err);
                reject(err);
                return;
              }
              console.log(`Direct stream download successful: ${outputPath}`);
              resolve(true);
            });
          });

          writeStream.on('error', (err) => {
            console.error('Write stream error:', err);
            // Clean up temp file if it exists
            if (fs.existsSync(tempFilePath)) {
              fs.unlinkSync(tempFilePath);
            }
            reject(err);
          });

          // Use a more direct approach with fetch
          const fetchVideo = async () => {
            try {
              // First try to get a direct video URL using ytdl-core's getInfo
              const info = await ytdl.getInfo(videoURL);
              const format = ytdl.chooseFormat(info.formats, {quality: 'highest'});

              if (!format || !format.url) {
                throw new Error('No suitable format found');
              }

              console.log(`Got direct video URL: ${format.url.substring(0, 50)}...`);

              // Create an HTTP request to the video URL
              const req = https.get(format.url, (res) => {
                if (res.statusCode !== 200) {
                  writeStream.end();
                  reject(new Error(`Failed to download video: HTTP ${res.statusCode}`));
                  return;
                }

                // Pipe the response to the file
                res.pipe(writeStream);

                res.on('error', (err) => {
                  console.error('Response error:', err);
                  writeStream.end();
                  reject(err);
                });
              });

              req.on('error', (err) => {
                console.error('Request error:', err);
                writeStream.end();
                reject(err);
              });
            } catch (err) {
              console.error('Error in fetchVideo:', err);
              writeStream.end();
              reject(err);
            }
          };

          // Start the fetch process
          fetchVideo();
        } catch (err) {
          console.error('Direct stream setup error:', err);
          reject(err);
        }
      });
    },
  },

  events: {},
};
