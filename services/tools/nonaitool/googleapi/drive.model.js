const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const {DRIVE_FILE, FILE} = require('../../../../constants/dbCollections');

const {Schema} = mongoose;
const schema = new Schema({
  episodeId: {type: Schema.Types.ObjectId, ref: FILE},
  fileId: {type: String},
  fileName: {type: String},
  webViewLink: {type: String},
}, {
  timestamps: {
    createdAt: 'createdAt', updatedAt: 'updatedAt',
  },
  versionKey: false,
});

schema.plugin(mongoosePaginate);

module.exports = mongoose.model(DRIVE_FILE, schema, DRIVE_FILE);