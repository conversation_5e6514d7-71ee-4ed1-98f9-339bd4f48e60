'use strict';
const {google} = require('googleapis');
const MODEL = require('./drive.model');
const BaseService = require('../../../../mixins/baseService.mixin');
const AdminService = require('../../../../mixins/adminService.mixin');
const DbMongoose = require('../../../../mixins/dbMongo.mixin');
const {GoogleAuth} = require('google-auth-library');
const fs = require('fs');
const path = require('path');
const FileMixin = require('../../../../mixins/file.mixin');
const {MoleculerClientError} = require('moleculer').Errors;
const {PassThrough} = require('stream');

const ffmpegPath = require('@ffmpeg-installer/ffmpeg').path;
const ffmpeg = require('fluent-ffmpeg');
ffmpeg.setFfmpegPath(ffmpegPath);

const storageDir = path.join(__dirname, 'storage');

module.exports = {
  name: 'drives',
  mixins: [DbMongoose(MODEL), BaseService, AdminService, FileMixin],
  settings: {
    populates: {},
    populateOptions: [],
  },

  actions: {
    uploadMovie: {
      rest: 'POST /movie',
      async handler(ctx) {
        const {movieId} = ctx.params;

        const {movie, episodes} = await ctx.call('movies.get', {id: movieId});

        if (!movie || !episodes || episodes.length === 0) {
          throw new MoleculerClientError('Movie or episodes not found');
        }

        const authClient = await this.authenticate();
        const drive = google.drive({version: 'v3', auth: authClient});

        const folderId = await this.getFolderDrive(drive, movie.slug);
        if (!folderId) {
          throw new MoleculerClientError(`Failed to get or create Google Drive folder for movie: ${movie.slug}`);
        }

        const uploadedFilesData = [];

        for (const episode of episodes) {
          for (let i = 0; i < episode.link.length; i++) {
            try {
              const fileData = await this.uploadFileToDrive(drive, folderId, movie.slug, episode, episode.link[i], i);
              // const fileData = await this.streamVideoToDrive(drive, folderId, movie.slug, episode, episode.link[i], i);
              uploadedFilesData.push(fileData);

            } catch (error) {
              this.logger.error(error.message);
            }
          }
        }

        return uploadedFilesData;
      },
    },
  },

  methods: {
    async authenticate() {
      try {
        const keyFilePath = path.join(__dirname, 'credential.json');
        const auth = new GoogleAuth({
          keyFilename: keyFilePath,
          scopes: ['https://www.googleapis.com/auth/drive.file'],
        });
        const authClient = await auth.getClient();
        return authClient;
      } catch (err) {
        throw new Error(`Google Drive authentication failed: ${err.message}`);
      }
    },
    async getFolderDrive(drive, folderName) {
      const parentFolderId = '194VCeKNYYQJL4eaMRInSVqjxEhouWIQw';

      const query = `mimeType='application/vnd.google-apps.folder' and name='${folderName}' and '${parentFolderId}' in parents and trashed=false`;
      try {
        const res = await drive.files.list({
          q: query,
          fields: 'files(id, name)',
          spaces: 'drive',
        });

        if (res.data.files && res.data.files.length > 0) {
          const foundFolderId = res.data.files[0].id;
          return foundFolderId;
        }

        const folder = await drive.files.create({
          requestBody: {
            name: folderName,
            mimeType: 'application/vnd.google-apps.folder',
            parents: [parentFolderId],
          },
          fields: 'id, name',
        });
        return folder.data.id;
      } catch (error) {
        return null;
      }
    },
    async uploadFileToDrive(drive, folderId, movieSlug, episode, videoUrl, videoIndex) {
      const videoName = `${movieSlug}-${episode.name}-${videoIndex}.mp4`;
      const videoPath = this.getFilePath(videoName, this.getDirPath(movieSlug, storageDir));

      try {
        await this.saveVideo(videoUrl, videoPath);

        const file = await drive.files.create({
          requestBody: {
            name: videoName,
            parents: [folderId],
          },
          media: {
            mimeType: 'video/mp4',
            body: fs.createReadStream(videoPath),
          },
          fields: 'id, name, webViewLink',
        });

        await this.adapter.insert({
          episodeId: episode._id,
          fileId: file.data.id,
          fileName: file.data.name,
          webViewLink: file.data.webViewLink,
        });

        return file.data;
      } catch (error) {
        throw error;
      } finally {
        try {
          if (fs.existsSync(videoPath)) {
            await fs.promises.unlink(videoPath);
          }
        } catch (e) {
          this.logger.error(e.message);
        }
      }
    },

    // async streamVideoToDrive(drive, folderId, movieSlug, episode, videoUrl, videoIndex) {
    //   const videoName = `${movieSlug}-${episode.name}-${videoIndex}.mp4`;
    //
    //   const passThroughStream = new PassThrough();
    //   let ffmpegCommand = null;
    //   let ffmpegErrored = false;
    //
    //   try {
    //     ffmpegCommand = ffmpeg(videoUrl)
    //       .outputFormat('mp4')
    //       // Use specific settings for m3u8 streams
    //       .outputOptions([
    //         '-c:v', 'libx264',
    //         '-c:a', 'aac',
    //         '-bsf:a', 'aac_adtstoasc',
    //         '-movflags', 'faststart'
    //       ])
    //       .on('start', (commandLine) => {
    //         this.logger.debug(`FFmpeg command started for ${videoName}: ${commandLine}`);
    //       })
    //       .on('progress', (progress) => {
    //         if (progress.percent && progress.percent >= 0) {
    //           this.logger.debug(`FFmpeg progress for ${videoName}: ${progress.percent.toFixed(2)}%`);
    //         }
    //       })
    //       .on('error', (err, stdout, stderr) => {
    //         ffmpegErrored = true;
    //         this.logger.error(`FFmpeg error for ${videoName}:`, err, {stdout, stderr});
    //       })
    //       .on('end', () => {
    //         this.logger.info(`FFmpeg finished piping data for ${videoName}. Upload should complete shortly.`);
    //       })
    //       .pipe(passThroughStream, {end: true});
    //
    //     // --- 2. Thực hiện Upload lên Google Drive ---
    //     this.logger.info(`Starting Google Drive upload for ${videoName}...`);
    //     const fileMetadata = {
    //       name: videoName,
    //       parents: [folderId],
    //     };
    //     const media = {
    //       mimeType: 'video/mp4',
    //       body: passThroughStream,
    //     };
    //
    //     // Sử dụng await
    //     const file = await drive.files.create({
    //       requestBody: fileMetadata,
    //       media: media,
    //       fields: 'id, name, webViewLink',
    //     });
    //
    //     if (ffmpegErrored) {
    //       this.logger.error(`Upload for ${videoName} reported success by Drive API (File ID: ${file.data.id}), but FFmpeg errored earlier. Aborting DB save and attempting cleanup.`);
    //       // Cố gắng xóa file lỗi trên Drive
    //       try {
    //         await drive.files.delete({fileId: file.data.id});
    //         this.logger.warn(`Deleted potentially corrupt Drive file ${file.data.id} for ${videoName} due to earlier FFmpeg error.`);
    //       } catch (deleteError) {
    //         this.logger.error(`Failed to delete potentially corrupt Drive file ${file.data.id} after FFmpeg error: ${deleteError.message}`);
    //       }
    //       // Ném lỗi để dừng quá trình và báo lỗi đúng
    //       throw new MoleculerClientError(`FFmpeg error occurred during upload process for ${videoName}, preventing DB save.`, 500, 'UPLOAD_ERROR_POST_FFMPEG_FAIL', {driveFile: file.data});
    //     }
    //
    //     // Save file information to database
    //     await this.adapter.insert({
    //       episodeId: episode._id,
    //       fileId: file.data.id,
    //       fileName: file.data.name,
    //       webViewLink: file.data.webViewLink,
    //     });
    //
    //     this.logger.info(`Google Drive upload successful for ${videoName}. File ID: ${file.data.id}`);
    //     return file.data;
    //
    //   } catch (error) {
    //     this.logger.error(`An error occurred in streamVideoToDrive for ${videoName}: ${error.message}`);
    //     throw error;
    //   }
    // },
  },

  events: {
    sseEventName(jobId) {
      return `sse.movie_status.${jobId}`;
    },
  },
};
