const config = {
  production: {
    websiteName: 'Demo',
    domain: process.env.DOMAIN || 'https://demo.stealersmile.click',
    client_id: process.env.GOOGLE_CLIENT_ID,
    client_secret: process.env.GOOGLE_CLIENT_SECRET,
    redirect_uri: `${process.env.DOMAIN || "https://demo.stealersmile.click"}/auth/login-google`,
    backend_base_url: process.env.BACKEND_BASE_URL || 'http://localhost:3000',
    supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
    mail: {
      host: process.env.MAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.MAIL_PORT) || 587,
      secure: process.env.MAIL_SECURE === 'true',
      auth: {
        user: process.env.MAIL_USER,
        pass: process.env.MAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false
      }
    },
  },
  development: {
    websiteName: 'Demo',
    supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
    mail: {
      host: process.env.MAIL_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.MAIL_PORT) || 587,
      secure: process.env.MAIL_SECURE === 'true',
      auth: {
        user: process.env.MAIL_USER,
        pass: process.env.MAIL_PASS,
      },
      tls: {
        rejectUnauthorized: false
      }
    },
    domain: process.env.DOMAIN || 'http://localhost:8080',
    client_id: process.env.GOOGLE_CLIENT_ID,
    client_secret: process.env.GOOGLE_CLIENT_SECRET,
    redirect_uri: `${process.env.DOMAIN || 'http://localhost:8080'}/auth/login-google`,
    backend_base_url: process.env.BACKEND_BASE_URL || '',
  },
};

exports.getConfig = env => config[env] || config.development;
