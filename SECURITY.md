# Security Guidelines

## Vấn đề bảo mật đã được sửa

### 1. Hardcoded Credentials ✅
**Vấn đề:** Credentials được hardcode trong `config/config.js`
**G<PERSON><PERSON>i pháp:** <PERSON> chuyển tất cả credentials sang environment variables

**Tr<PERSON>ớ<PERSON>:**
```javascript
client_secret: "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",
auth: {
  user: '<EMAIL>',
  pass: 'ymxi simf kpyf rolx',
}
```

**Sau:**
```javascript
client_secret: process.env.GOOGLE_CLIENT_SECRET,
auth: {
  user: process.env.MAIL_USER,
  pass: process.env.MAIL_PASS,
}
```

### 2. Input Validation ✅
**Vấn đề:** Thiếu validation cho input parameters
**Giải pháp:** Thêm validation cho các actions

## <PERSON><PERSON>yến nghị bả<PERSON> mậ<PERSON> bổ sung

### 1. Environment Variables
- <PERSON><PERSON> dụng `.env` file cho development
- Sử dụng secure secret management cho production
- Không commit `.env` file vào git

### 2. Input Sanitization
```javascript
// Thêm vào tools.service.js
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};
```

### 3. Rate Limiting
```javascript
// Thêm middleware rate limiting
const rateLimit = {
  window: 60000, // 1 minute
  limit: 100,    // 100 requests per minute
  headers: true
};
```

### 4. Authentication & Authorization
- Implement proper JWT validation
- Add role-based access control
- Validate user permissions for each action

### 5. API Security Headers
```javascript
// Thêm security headers
app.use((req, res, next) => {
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  next();
});
```

### 6. Database Security
- Use parameterized queries (đã có với Mongoose)
- Implement proper indexing
- Regular security updates

### 7. File Upload Security
```javascript
// Validate file types and sizes
const allowedTypes = ['image/jpeg', 'image/png', 'application/pdf'];
const maxSize = 10 * 1024 * 1024; // 10MB
```

### 8. Logging & Monitoring
- Log security events
- Monitor for suspicious activities
- Implement alerting for security incidents

### 9. Error Handling
- Don't expose sensitive information in error messages
- Use generic error messages for client
- Log detailed errors server-side only

### 10. Dependencies
- Regular dependency updates
- Use `npm audit` to check vulnerabilities
- Implement dependency scanning in CI/CD

## Checklist bảo mật

- [x] Remove hardcoded credentials
- [x] Add input validation
- [ ] Implement rate limiting
- [ ] Add security headers
- [ ] Implement proper authentication
- [ ] Add input sanitization
- [ ] Set up monitoring
- [ ] Regular security audits
- [ ] Dependency vulnerability scanning
- [ ] Error handling improvements
